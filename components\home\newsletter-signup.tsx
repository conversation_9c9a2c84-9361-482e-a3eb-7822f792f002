"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Mail, Gift } from "lucide-react"

export function NewsletterSignup() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Reset form
    setEmail("")
    setIsLoading(false)
    
    // Show success message (you would implement toast here)
    alert("Thank you for subscribing!")
  }

  return (
    <section className="bg-blue-600 py-16">
      <div className="container px-4">
        <div className="max-w-2xl mx-auto text-center text-white">
          <div className="flex justify-center mb-6">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
              <Mail className="h-8 w-8" />
            </div>
          </div>
          
          <h2 className="text-3xl font-bold mb-4">Stay in the Loop</h2>
          <p className="text-blue-100 mb-8 text-lg">
            Subscribe to our newsletter and be the first to know about new products, 
            exclusive deals, and AI-powered recommendations just for you.
          </p>

          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-blue-200"
            />
            <Button 
              type="submit" 
              variant="secondary" 
              loading={isLoading}
              className="whitespace-nowrap"
            >
              Subscribe Now
            </Button>
          </form>

          <div className="flex items-center justify-center mt-6 text-blue-100">
            <Gift className="h-4 w-4 mr-2" />
            <span className="text-sm">Get 10% off your first order when you subscribe!</span>
          </div>

          <p className="text-xs text-blue-200 mt-4">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </div>
    </section>
  )
}
