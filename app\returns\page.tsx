import { Breadcrumb } from "@/components/ui/breadcrumb"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { RotateCcw, Shield, Clock, CheckCircle } from "lucide-react"
import Link from "next/link"

export default function ReturnsPage() {
  return (
    <div className="container px-4 py-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Returns", href: "/returns" },
        ]}
      />

      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Returns & Exchanges</h1>
          <p className="text-lg text-muted-foreground">
            Easy returns and exchanges with our 30-day guarantee
          </p>
        </div>

        {/* Return Policy Highlight */}
        <Card className="mb-8 bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Shield className="h-12 w-12 mx-auto text-blue-600 mb-4" />
              <h2 className="text-2xl font-bold text-blue-800 mb-2">30-Day Return Policy</h2>
              <p className="text-blue-700">Not satisfied? Return your items within 30 days for a full refund</p>
            </div>
          </CardContent>
        </Card>

        {/* Return Process Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center">How to Return an Item</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h3 className="font-semibold mb-2">Start Return</h3>
                <p className="text-sm text-muted-foreground">Log into your account and select the item to return</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">2</span>
                </div>
                <h3 className="font-semibold mb-2">Print Label</h3>
                <p className="text-sm text-muted-foreground">Print the prepaid return shipping label</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold">3</span>
                </div>
                <h3 className="font-semibold mb-2">Pack & Ship</h3>
                <p className="text-sm text-muted-foreground">Pack the item and drop it off at any carrier location</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Get Refund</h3>
                <p className="text-sm text-muted-foreground">Receive your refund within 5-7 business days</p>
              </div>
            </div>
            
            <div className="text-center mt-8">
              <Button size="lg" asChild>
                <Link href="/account/orders">Start a Return</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Return Policy Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-blue-600" />
                    Time Limit
                  </h3>
                  <p className="text-muted-foreground">
                    Items must be returned within 30 days of delivery date.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2 flex items-center">
                    <RotateCcw className="h-5 w-5 mr-2 text-green-600" />
                    Condition
                  </h3>
                  <p className="text-muted-foreground">
                    Items must be in original condition with tags attached.
                  </p>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">What Can Be Returned</h3>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Most clothing and accessories</li>
                  <li>Electronics in original packaging</li>
                  <li>Home goods and decor items</li>
                  <li>Books and media (if unopened)</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">What Cannot Be Returned</h3>
                <ul className="list-disc list-inside text-muted-foreground space-y-1">
                  <li>Personal care items and cosmetics</li>
                  <li>Customized or personalized items</li>
                  <li>Perishable goods</li>
                  <li>Digital downloads</li>
                  <li>Gift cards</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Exchanges</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Size Exchanges</h3>
                <p className="text-muted-foreground">
                  Need a different size? We offer free size exchanges for most clothing items. 
                  Simply return the original item and place a new order for the correct size.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Color/Style Exchanges</h3>
                <p className="text-muted-foreground">
                  Want a different color or style? Return the original item and place a new order. 
                  If there's a price difference, we'll refund or charge the difference.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Refund Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Processing Time</h3>
                <p className="text-muted-foreground">
                  Refunds are processed within 2-3 business days after we receive your return. 
                  It may take an additional 3-5 business days for the refund to appear in your account.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Refund Method</h3>
                <p className="text-muted-foreground">
                  Refunds are issued to the original payment method. For gift card purchases, 
                  refunds will be issued as store credit.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Return Shipping</h3>
                <p className="text-muted-foreground">
                  We provide prepaid return labels for most returns. Return shipping costs will be 
                  deducted from your refund unless the return is due to our error.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Have questions about returns or need assistance? We're here to help!
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Contact Support</h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +****************</p>
                    <p><strong>Hours:</strong> Mon-Fri 9AM-6PM EST</p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Live Chat</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Get instant help with our AI assistant
                  </p>
                  <Button variant="outline" size="sm">Start Chat</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
