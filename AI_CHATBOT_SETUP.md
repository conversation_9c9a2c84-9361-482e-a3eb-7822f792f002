# 🤖 AI Chatbot Setup Guide

Your ecommerce application now includes a **real AI-powered chatbot** using Google's Gemini API. Follow these steps to activate it:

## 🚀 Quick Setup (5 minutes)

### Step 1: Get Your Free Gemini API Key

1. **Visit Google AI Studio**: Go to [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)

2. **Sign in** with your Google account

3. **Create API Key**: Click "Create API Key" button

4. **Copy the key**: Save it securely - you'll need it in the next step

### Step 2: Add API Key to Your Environment

1. **Create `.env.local` file** in your project root (if it doesn't exist)

2. **Add your API key**:
   ```bash
   GEMINI_API_KEY="your-actual-api-key-here"
   ```

3. **Restart your development server**:
   ```bash
   npm run dev
   ```

### Step 3: Test Your AI Chatbot

1. **Open your application** at `http://localhost:3000`

2. **Click the AI bot icon** (bottom-right corner)

3. **Try these test messages**:
   - "I'm looking for wireless headphones under $200"
   - "Show me blue sneakers"
   - "What electronics do you have?"
   - "I need help with shipping"

## 🎯 What Your AI Chatbot Can Do

### **Product Recommendations**
- Understands natural language queries
- Recommends specific products with prices
- Filters by category, price range, brand
- Suggests alternatives when products aren't available

### **Customer Support**
- Answers shipping and return policy questions
- Provides store information and contact details
- Helps with payment and checkout questions
- Guides users through the shopping process

### **Smart Conversations**
- Remembers context within the conversation
- Asks follow-up questions to better understand needs
- Provides personalized recommendations
- Uses friendly, conversational tone

## 🔧 Advanced Configuration

### Alternative AI Providers

If you prefer other AI services, you can easily switch:

#### **OpenAI ChatGPT**
```bash
# Add to .env.local
OPENAI_API_KEY="your-openai-api-key"
```

#### **Anthropic Claude**
```bash
# Add to .env.local
ANTHROPIC_API_KEY="your-anthropic-api-key"
```

### Customizing the AI Personality

Edit the `PRODUCT_CONTEXT` in `/app/api/chatbot/route.ts` to:
- Change the AI's personality and tone
- Add more product information
- Update store policies
- Modify response guidelines

### Rate Limiting & Costs

**Gemini API Free Tier:**
- 60 requests per minute
- 1,500 requests per day
- Perfect for development and small-scale production

**For Production:**
- Consider implementing rate limiting
- Add user authentication to prevent abuse
- Monitor API usage in Google Cloud Console

## 🛡️ Fallback System

Your chatbot includes a **smart fallback system**:

1. **Primary**: Uses Gemini AI for dynamic responses
2. **Fallback**: If API fails, uses pre-defined intelligent responses
3. **Always Available**: Chatbot never goes completely offline

## 🎨 Customization Options

### **Chatbot Appearance**
- Edit `/components/chatbot/chatbot.tsx` for UI changes
- Modify colors, size, position, animations
- Add your brand colors and styling

### **Product Database**
- Update `PRODUCT_CONTEXT` with your actual inventory
- Connect to real product database
- Add real-time inventory updates

### **Response Formatting**
- Modify AI prompts for different response styles
- Add emoji usage guidelines
- Customize conversation flow

## 📊 Monitoring & Analytics

### **Track Chatbot Performance**
```javascript
// Add to your analytics
gtag('event', 'chatbot_interaction', {
  'message_type': 'product_search',
  'user_intent': 'purchase_intent'
});
```

### **Common Metrics to Track**
- Conversation completion rate
- Product recommendation click-through
- Customer satisfaction scores
- Most common queries

## 🚀 Production Deployment

### **Environment Variables for Production**
```bash
# Production .env
GEMINI_API_KEY="your-production-api-key"
NEXTAUTH_URL="https://yourdomain.com"
DATABASE_URL="your-production-database-url"
```

### **Security Best Practices**
- Never expose API keys in client-side code
- Use environment variables for all secrets
- Implement rate limiting for API endpoints
- Add input validation and sanitization

## 🎉 You're All Set!

Your AI chatbot is now ready to provide intelligent, personalized shopping assistance to your customers. The AI understands your product catalog and can help customers find exactly what they're looking for.

**Need Help?** 
- Check the console for any error messages
- Verify your API key is correctly set
- Ensure your development server is running
- Test with simple queries first

**Pro Tip**: The AI gets smarter over time as you add more product information and refine the prompts!
