"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { Brain, TrendingUp, Users, Zap } from "lucide-react"

const features = [
  {
    icon: Brain,
    title: "Smart Recommendations",
    description: "Our AI analyzes your preferences and browsing history to suggest products you'll love.",
  },
  {
    icon: TrendingUp,
    title: "Dynamic Pricing",
    description: "Get the best deals with our machine learning-powered pricing optimization.",
  },
  {
    icon: Users,
    title: "Social Shopping",
    description: "Discover what others like you are buying with collaborative filtering.",
  },
  {
    icon: Zap,
    title: "Instant Insights",
    description: "Real-time inventory updates and personalized shopping assistance.",
  },
]

export function AIRecommendations() {
  return (
    <section className="bg-gray-50 py-16">
      <div className="container px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            Powered by Artificial Intelligence
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Experience the future of shopping with our AI-driven features that make 
            finding the perfect products effortless and enjoyable.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">
            Ready to Experience AI-Powered Shopping?
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Join thousands of satisfied customers who have discovered the perfect products 
            with our intelligent recommendation system.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              Start Shopping Now
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              Learn More About Our AI
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
