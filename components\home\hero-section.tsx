"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>Right, ShoppingBag, Sparkles } from "lucide-react"

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container px-4 py-24 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8">
          <div className="flex flex-col justify-center space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                <Sparkles className="mr-2 h-4 w-4" />
                AI-Powered Shopping Experience
              </div>
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                Discover Amazing Products with{" "}
                <span className="text-blue-600">Smart Recommendations</span>
              </h1>
              <p className="text-lg text-gray-600">
                Experience the future of online shopping with our AI-powered platform. 
                Get personalized recommendations, secure checkout, and exceptional customer service.
              </p>
            </div>
            
            <div className="flex flex-col gap-4 sm:flex-row">
              <Button size="lg" asChild>
                <Link href="/products">
                  <ShoppingBag className="mr-2 h-5 w-5" />
                  Start Shopping
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/about">
                  Learn More
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-8 pt-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">10K+</div>
                <div className="text-sm text-gray-600">Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">50K+</div>
                <div className="text-sm text-gray-600">Happy Customers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">99.9%</div>
                <div className="text-sm text-gray-600">Uptime</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square overflow-hidden rounded-2xl bg-gradient-to-br from-blue-400 to-purple-600 p-8">
              <div className="flex h-full items-center justify-center">
                <div className="text-center text-white">
                  <ShoppingBag className="mx-auto h-24 w-24 mb-4" />
                  <h3 className="text-2xl font-bold mb-2">Your Shopping Journey Starts Here</h3>
                  <p className="text-blue-100">Powered by cutting-edge AI technology</p>
                </div>
              </div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-yellow-400 opacity-20"></div>
            <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-pink-400 opacity-20"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
