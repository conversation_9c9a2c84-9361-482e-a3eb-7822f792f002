import { Breadcrumb } from "@/components/ui/breadcrumb"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Truck, Clock, Globe, Package } from "lucide-react"

export default function ShippingPage() {
  return (
    <div className="container px-4 py-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Shipping Info", href: "/shipping" },
        ]}
      />

      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Shipping Information</h1>
          <p className="text-lg text-muted-foreground">
            Everything you need to know about our shipping options and policies
          </p>
        </div>

        {/* Free Shipping Banner */}
        <Card className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Truck className="h-12 w-12 mx-auto text-green-600 mb-4" />
              <h2 className="text-2xl font-bold text-green-800 mb-2">Free Shipping</h2>
              <p className="text-green-700">On all orders over $50 within the United States</p>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="text-center">
              <Package className="h-12 w-12 mx-auto text-blue-600 mb-4" />
              <CardTitle>Standard Shipping</CardTitle>
              <Badge variant="secondary">3-5 Business Days</Badge>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-2xl font-bold mb-2">$5.99</p>
              <p className="text-muted-foreground">Free on orders over $50</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Clock className="h-12 w-12 mx-auto text-orange-600 mb-4" />
              <CardTitle>Express Shipping</CardTitle>
              <Badge variant="secondary">1-2 Business Days</Badge>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-2xl font-bold mb-2">$12.99</p>
              <p className="text-muted-foreground">Expedited delivery</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Globe className="h-12 w-12 mx-auto text-purple-600 mb-4" />
              <CardTitle>International</CardTitle>
              <Badge variant="secondary">7-14 Business Days</Badge>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-2xl font-bold mb-2">$19.99+</p>
              <p className="text-muted-foreground">Varies by destination</p>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Policies</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Processing Time</h3>
                <p className="text-muted-foreground">
                  Orders are typically processed within 1-2 business days. Orders placed on weekends 
                  or holidays will be processed the next business day.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Shipping Confirmation</h3>
                <p className="text-muted-foreground">
                  You'll receive a shipping confirmation email with tracking information once your 
                  order has been dispatched.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Address Accuracy</h3>
                <p className="text-muted-foreground">
                  Please ensure your shipping address is correct. We are not responsible for packages 
                  sent to incorrect addresses provided by the customer.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>International Shipping</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Available Countries</h3>
                <p className="text-muted-foreground">
                  We ship to most countries worldwide. Shipping costs and delivery times vary by destination.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Customs and Duties</h3>
                <p className="text-muted-foreground">
                  International customers are responsible for any customs duties, taxes, or fees 
                  imposed by their country. These charges are not included in our shipping costs.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Restricted Items</h3>
                <p className="text-muted-foreground">
                  Some products may have shipping restrictions to certain countries due to local 
                  regulations. These restrictions will be noted on the product page.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Order Tracking</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Track Your Package</h3>
                <p className="text-muted-foreground">
                  Once your order ships, you'll receive a tracking number via email. You can also 
                  track your order by logging into your account and viewing your order history.
                </p>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">Delivery Issues</h3>
                <p className="text-muted-foreground">
                  If your package is marked as delivered but you haven't received it, please check 
                  with neighbors and contact us within 48 hours.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                Have questions about shipping? Our customer service team is here to help!
              </p>
              <div className="space-y-2">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +****************</p>
                <p><strong>Hours:</strong> Monday - Friday, 9 AM - 6 PM EST</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
