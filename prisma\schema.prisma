// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(CUSTOMER)
  isActive      Boolean   @default(true)
  twoFactorEnabled Boolean @default(false)
  twoFactorSecret String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  profile       UserProfile?
  addresses     Address[]
  orders        Order[]
  reviews       Review[]
  wishlist      WishlistItem[]
  cart          CartItem[]
  chatSessions  ChatSession[]
  recommendations UserRecommendation[]
  behaviorLogs  UserBehaviorLog[]
}

model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  dateOfBirth DateTime?
  gender      Gender?
  preferences Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Address {
  id           String      @id @default(cuid())
  userId       String
  type         AddressType @default(SHIPPING)
  firstName    String
  lastName     String
  company      String?
  addressLine1 String
  addressLine2 String?
  city         String
  state        String
  postalCode   String
  country      String
  phone        String?
  isDefault    Boolean     @default(false)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]
}

// Product Catalog
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
}

model Product {
  id              String        @id @default(cuid())
  name            String
  slug            String        @unique
  description     String?
  shortDescription String?
  sku             String        @unique
  price           Decimal       @db.Decimal(10, 2)
  comparePrice    Decimal?      @db.Decimal(10, 2)
  costPrice       Decimal?      @db.Decimal(10, 2)
  trackQuantity   Boolean       @default(true)
  quantity        Int           @default(0)
  weight          Decimal?      @db.Decimal(8, 2)
  dimensions      Json?
  isActive        Boolean       @default(true)
  isFeatured      Boolean       @default(false)
  tags            String[]
  metaTitle       String?
  metaDescription String?
  categoryId      String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  category     Category       @relation(fields: [categoryId], references: [id])
  images       ProductImage[]
  variants     ProductVariant[]
  reviews      Review[]
  cartItems    CartItem[]
  orderItems   OrderItem[]
  wishlistItems WishlistItem[]
  recommendations ProductRecommendation[]
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model ProductVariant {
  id        String   @id @default(cuid())
  productId String
  name      String
  sku       String   @unique
  price     Decimal  @db.Decimal(10, 2)
  quantity  Int      @default(0)
  options   Json     // Store variant options like color, size, etc.
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

// Shopping Cart & Wishlist
model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  variantId String?
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, variantId])
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  userId          String?
  orderNumber     String      @unique
  status          OrderStatus @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING)
  fulfillmentStatus FulfillmentStatus @default(UNFULFILLED)
  subtotal        Decimal     @db.Decimal(10, 2)
  taxAmount       Decimal     @db.Decimal(10, 2)
  shippingAmount  Decimal     @db.Decimal(10, 2)
  discountAmount  Decimal     @db.Decimal(10, 2) @default(0)
  totalAmount     Decimal     @db.Decimal(10, 2)
  currency        String      @default("USD")
  shippingAddressId String?
  billingAddressId String?
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  user            User?       @relation(fields: [userId], references: [id])
  shippingAddress Address?    @relation(fields: [shippingAddressId], references: [id])
  items           OrderItem[]
  payments        Payment[]
  shipments       Shipment[]
}

model OrderItem {
  id          String  @id @default(cuid())
  orderId     String
  productId   String
  variantId   String?
  quantity    Int
  price       Decimal @db.Decimal(10, 2)
  totalPrice  Decimal @db.Decimal(10, 2)
  productName String
  productSku  String

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  method          PaymentMethod
  transactionId   String?
  gatewayResponse Json?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Shipment {
  id            String           @id @default(cuid())
  orderId       String
  trackingNumber String?
  carrier       String?
  status        ShipmentStatus   @default(PENDING)
  shippedAt     DateTime?
  deliveredAt   DateTime?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt

  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

// Reviews & Ratings
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  isApproved Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

// AI & Chatbot
model ChatSession {
  id        String        @id @default(cuid())
  userId    String?
  sessionId String        @unique
  status    ChatStatus    @default(ACTIVE)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  user     User?         @relation(fields: [userId], references: [id])
  messages ChatMessage[]
}

model ChatMessage {
  id            String      @id @default(cuid())
  sessionId     String
  role          MessageRole
  content       String
  metadata      Json?
  createdAt     DateTime    @default(now())

  session ChatSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
}

model UserRecommendation {
  id        String   @id @default(cuid())
  userId    String
  productId String
  score     Float
  reason    String?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ProductRecommendation {
  id                String   @id @default(cuid())
  productId         String
  recommendedProductId String
  score             Float
  type              RecommendationType
  createdAt         DateTime @default(now())

  product           Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model UserBehaviorLog {
  id        String       @id @default(cuid())
  userId    String?
  sessionId String?
  action    BehaviorAction
  entityType String?
  entityId  String?
  metadata  Json?
  createdAt DateTime     @default(now())

  user User? @relation(fields: [userId], references: [id])
}

// Enums
enum UserRole {
  CUSTOMER
  ADMIN
  MODERATOR
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

enum AddressType {
  SHIPPING
  BILLING
  BOTH
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum FulfillmentStatus {
  UNFULFILLED
  PARTIAL
  FULFILLED
}

enum PaymentMethod {
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  APPLE_PAY
  GOOGLE_PAY
  BANK_TRANSFER
  CASH_ON_DELIVERY
}

enum ShipmentStatus {
  PENDING
  PROCESSING
  SHIPPED
  IN_TRANSIT
  DELIVERED
  RETURNED
}

enum ChatStatus {
  ACTIVE
  CLOSED
  ARCHIVED
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum RecommendationType {
  SIMILAR
  FREQUENTLY_BOUGHT_TOGETHER
  TRENDING
  PERSONALIZED
}

enum BehaviorAction {
  VIEW_PRODUCT
  ADD_TO_CART
  REMOVE_FROM_CART
  ADD_TO_WISHLIST
  REMOVE_FROM_WISHLIST
  SEARCH
  PURCHASE
  REVIEW
  CHAT_MESSAGE
}
