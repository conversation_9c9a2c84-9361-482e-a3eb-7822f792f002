// Simplified Prisma schema for SQLite
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// User Management
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("CUSTOMER")
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  profile       UserProfile?
  orders        Order[]
  reviews       Review[]
  wishlist      WishlistItem[]
  cart          CartItem[]
  chatSessions  ChatSession[]
  recommendations UserRecommendation[]
  behaviorLogs  UserBehaviorLog[]
}

model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  firstName   String?
  lastName    String?
  phone       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Product Catalog
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  slug        String    @unique
  description String?
  image       String?
  parentId    String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
}

model Product {
  id              String        @id @default(cuid())
  name            String
  slug            String        @unique
  description     String?
  shortDescription String?
  sku             String        @unique
  price           Float
  comparePrice    Float?
  trackQuantity   Boolean       @default(true)
  quantity        Int           @default(0)
  isActive        Boolean       @default(true)
  isFeatured      Boolean       @default(false)
  tags            String?       // Comma-separated tags
  metaTitle       String?
  metaDescription String?
  categoryId      String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  category     Category       @relation(fields: [categoryId], references: [id])
  images       ProductImage[]
  reviews      Review[]
  cartItems    CartItem[]
  orderItems   OrderItem[]
  wishlistItems WishlistItem[]
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int      @default(0)
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

// Shopping Cart & Wishlist
model CartItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

// Order Management
model Order {
  id              String      @id @default(cuid())
  userId          String?
  orderNumber     String      @unique
  status          String      @default("PENDING")
  paymentStatus   String      @default("PENDING")
  subtotal        Float
  taxAmount       Float
  shippingAmount  Float
  discountAmount  Float       @default(0)
  totalAmount     Float
  currency        String      @default("USD")
  notes           String?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  user            User?       @relation(fields: [userId], references: [id])
  items           OrderItem[]
  payments        Payment[]
}

model OrderItem {
  id          String  @id @default(cuid())
  orderId     String
  productId   String
  quantity    Int
  price       Float
  totalPrice  Float
  productName String
  productSku  String

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
}

model Payment {
  id              String        @id @default(cuid())
  orderId         String
  amount          Float
  currency        String        @default("USD")
  status          String        @default("PENDING")
  method          String
  transactionId   String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

// Reviews & Ratings
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  title     String?
  comment   String?
  isVerified Boolean @default(false)
  isApproved Boolean @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
}

// AI & Chatbot
model ChatSession {
  id        String        @id @default(cuid())
  userId    String?
  sessionId String        @unique
  status    String        @default("ACTIVE")
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  user     User?         @relation(fields: [userId], references: [id])
  messages ChatMessage[]
}

model ChatMessage {
  id            String      @id @default(cuid())
  sessionId     String
  role          String      // USER, ASSISTANT, SYSTEM
  content       String
  createdAt     DateTime    @default(now())

  session ChatSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
}

model UserRecommendation {
  id        String   @id @default(cuid())
  userId    String
  productId String
  score     Float
  reason    String?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserBehaviorLog {
  id        String       @id @default(cuid())
  userId    String?
  sessionId String?
  action    String       // VIEW_PRODUCT, ADD_TO_CART, etc.
  entityType String?
  entityId  String?
  createdAt DateTime     @default(now())

  user User? @relation(fields: [userId], references: [id])
}
