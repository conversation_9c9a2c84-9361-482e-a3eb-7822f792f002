import { Breadcrumb } from "@/components/ui/breadcrumb"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Clock, Percent, Tag } from "lucide-react"
import Link from "next/link"

export default function DealsPage() {
  return (
    <div className="container px-4 py-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Deals", href: "/deals" },
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-4">Special Deals & Offers</h1>
        <p className="text-lg text-muted-foreground">
          Don't miss out on these amazing deals and limited-time offers
        </p>
      </div>

      {/* Featured Deal */}
      <Card className="mb-8 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl mb-2">Flash Sale - 50% Off Electronics</CardTitle>
              <p className="text-blue-100">Limited time offer on selected electronics</p>
            </div>
            <Badge variant="secondary" className="text-lg px-4 py-2">
              50% OFF
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Clock className="h-5 w-5" />
              <span>Ends in 2 days, 14 hours</span>
            </div>
            <Button variant="secondary" size="lg" asChild>
              <Link href="/products?category=electronics">Shop Now</Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Deal Categories */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="text-center">
          <CardHeader>
            <Percent className="h-12 w-12 mx-auto text-green-600 mb-4" />
            <CardTitle>Clearance Sale</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Up to 70% off on selected items. Limited stock available.
            </p>
            <Button variant="outline" asChild>
              <Link href="/products?sortBy=price&sortOrder=asc">View Clearance</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Tag className="h-12 w-12 mx-auto text-blue-600 mb-4" />
            <CardTitle>Daily Deals</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              New deals every day. Check back regularly for fresh offers.
            </p>
            <Button variant="outline" asChild>
              <Link href="/products?isFeatured=true">View Daily Deals</Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Clock className="h-12 w-12 mx-auto text-purple-600 mb-4" />
            <CardTitle>Limited Time</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Exclusive offers that won't last long. Act fast!
            </p>
            <Button variant="outline" asChild>
              <Link href="/products">Shop Limited Offers</Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Deal Tips */}
      <Card>
        <CardHeader>
          <CardTitle>How to Get the Best Deals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Sign up for notifications</h3>
              <p className="text-muted-foreground">
                Get notified about flash sales and exclusive member deals.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Follow us on social media</h3>
              <p className="text-muted-foreground">
                We often announce special promotions on our social channels.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Check back regularly</h3>
              <p className="text-muted-foreground">
                New deals are added daily, so make sure to visit frequently.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Bundle and save</h3>
              <p className="text-muted-foreground">
                Look for bundle deals to save even more on multiple items.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
