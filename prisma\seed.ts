import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create categories
  const electronics = await prisma.category.create({
    data: {
      name: 'Electronics',
      slug: 'electronics',
      description: 'Latest gadgets and tech',
      image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
    },
  })

  const fashion = await prisma.category.create({
    data: {
      name: 'Fashion',
      slug: 'fashion',
      description: 'Trendy clothing and accessories',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
    },
  })

  const home = await prisma.category.create({
    data: {
      name: 'Home & Garden',
      slug: 'home',
      description: 'Everything for your home',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    },
  })

  // Create products
  const products = [
    {
      name: 'Wireless Bluetooth Headphones',
      slug: 'wireless-bluetooth-headphones',
      description: 'High-quality wireless headphones with noise cancellation and long battery life.',
      shortDescription: 'Premium wireless headphones with noise cancellation',
      sku: 'WBH-001',
      price: 99.99,
      comparePrice: 149.99,
      categoryId: electronics.id,
      quantity: 50,
      isFeatured: true,
      tags: ['wireless', 'bluetooth', 'headphones', 'audio'],
      images: [
        {
          url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          altText: 'Wireless Bluetooth Headphones',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Smart Fitness Watch',
      slug: 'smart-fitness-watch',
      description: 'Advanced fitness tracking with heart rate monitoring, GPS, and smartphone integration.',
      shortDescription: 'Advanced fitness tracking smartwatch',
      sku: 'SFW-002',
      price: 199.99,
      comparePrice: 299.99,
      categoryId: electronics.id,
      quantity: 30,
      isFeatured: true,
      tags: ['smartwatch', 'fitness', 'health', 'gps'],
      images: [
        {
          url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
          altText: 'Smart Fitness Watch',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Portable Laptop Stand',
      slug: 'portable-laptop-stand',
      description: 'Ergonomic and adjustable laptop stand for better posture and productivity.',
      shortDescription: 'Ergonomic adjustable laptop stand',
      sku: 'PLS-003',
      price: 49.99,
      comparePrice: 79.99,
      categoryId: electronics.id,
      quantity: 75,
      tags: ['laptop', 'stand', 'ergonomic', 'portable'],
      images: [
        {
          url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          altText: 'Portable Laptop Stand',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Wireless Charging Pad',
      slug: 'wireless-charging-pad',
      description: 'Fast wireless charging pad compatible with all Qi-enabled devices.',
      shortDescription: 'Fast wireless charging pad',
      sku: 'WCP-004',
      price: 29.99,
      comparePrice: 49.99,
      categoryId: electronics.id,
      quantity: 100,
      isFeatured: true,
      tags: ['wireless', 'charging', 'qi', 'fast'],
      images: [
        {
          url: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
          altText: 'Wireless Charging Pad',
          sortOrder: 0,
        },
      ],
    },
  ]

  for (const productData of products) {
    const { images, ...product } = productData
    const createdProduct = await prisma.product.create({
      data: product,
    })

    // Create product images
    for (const image of images) {
      await prisma.productImage.create({
        data: {
          ...image,
          productId: createdProduct.id,
        },
      })
    }
  }

  console.log('Seed data created successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
