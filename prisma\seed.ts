import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create categories
  const electronics = await prisma.category.create({
    data: {
      name: 'Electronics',
      slug: 'electronics',
      description: 'Latest gadgets and tech',
      image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
    },
  })

  const fashion = await prisma.category.create({
    data: {
      name: 'Fashion',
      slug: 'fashion',
      description: 'Trendy clothing and accessories',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
    },
  })

  const home = await prisma.category.create({
    data: {
      name: 'Home & Garden',
      slug: 'home',
      description: 'Everything for your home',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
    },
  })

  const sports = await prisma.category.create({
    data: {
      name: 'Sports & Fitness',
      slug: 'sports',
      description: 'Gear for active lifestyle',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
    },
  })

  // Create products
  const products = [
    // Electronics
    {
      name: 'Wireless Bluetooth Headphones',
      slug: 'wireless-bluetooth-headphones',
      description: 'High-quality wireless headphones with noise cancellation and long battery life.',
      shortDescription: 'Premium wireless headphones with noise cancellation',
      sku: 'WBH-001',
      price: 99.99,
      comparePrice: 149.99,
      categoryId: electronics.id,
      quantity: 50,
      isFeatured: true,
      tags: 'wireless,bluetooth,headphones,audio',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          altText: 'Wireless Bluetooth Headphones',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Smart Fitness Watch',
      slug: 'smart-fitness-watch',
      description: 'Advanced fitness tracking with heart rate monitoring, GPS, and smartphone integration.',
      shortDescription: 'Advanced fitness tracking smartwatch',
      sku: 'SFW-002',
      price: 199.99,
      comparePrice: 299.99,
      categoryId: electronics.id,
      quantity: 30,
      isFeatured: true,
      tags: 'smartwatch,fitness,health,gps',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
          altText: 'Smart Fitness Watch',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Portable Laptop Stand',
      slug: 'portable-laptop-stand',
      description: 'Ergonomic and adjustable laptop stand for better posture and productivity.',
      shortDescription: 'Ergonomic adjustable laptop stand',
      sku: 'PLS-003',
      price: 49.99,
      comparePrice: 79.99,
      categoryId: electronics.id,
      quantity: 75,
      tags: 'laptop,stand,ergonomic,portable',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          altText: 'Portable Laptop Stand',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Wireless Charging Pad',
      slug: 'wireless-charging-pad',
      description: 'Fast wireless charging pad compatible with all Qi-enabled devices.',
      shortDescription: 'Fast wireless charging pad',
      sku: 'WCP-004',
      price: 29.99,
      comparePrice: 49.99,
      categoryId: electronics.id,
      quantity: 100,
      isFeatured: true,
      tags: 'wireless,charging,qi,fast',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop',
          altText: 'Wireless Charging Pad',
          sortOrder: 0,
        },
      ],
    },
    // Fashion
    {
      name: 'Blue Running Sneakers',
      slug: 'blue-running-sneakers',
      description: 'Comfortable blue running sneakers with advanced cushioning and breathable mesh.',
      shortDescription: 'Comfortable blue running sneakers',
      sku: 'BRS-005',
      price: 89.99,
      comparePrice: 120.00,
      categoryId: fashion.id,
      quantity: 40,
      isFeatured: true,
      tags: 'sneakers,blue,running,shoes,sports',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
          altText: 'Blue Running Sneakers',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Classic Denim Jacket',
      slug: 'classic-denim-jacket',
      description: 'Timeless denim jacket made from premium cotton with a comfortable fit.',
      shortDescription: 'Timeless denim jacket',
      sku: 'CDJ-006',
      price: 79.99,
      comparePrice: 110.00,
      categoryId: fashion.id,
      quantity: 25,
      tags: 'denim,jacket,classic,cotton',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',
          altText: 'Classic Denim Jacket',
          sortOrder: 0,
        },
      ],
    },
    // Sports
    {
      name: 'Professional Basketball',
      slug: 'professional-basketball',
      description: 'Official size basketball with superior grip and durability for indoor and outdoor play.',
      shortDescription: 'Official size basketball',
      sku: 'PBB-007',
      price: 34.99,
      comparePrice: 45.00,
      categoryId: sports.id,
      quantity: 60,
      tags: 'basketball,sports,official,grip',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop',
          altText: 'Professional Basketball',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Yoga Mat Premium',
      slug: 'yoga-mat-premium',
      description: 'Non-slip premium yoga mat with extra cushioning for comfortable practice.',
      shortDescription: 'Non-slip premium yoga mat',
      sku: 'YMP-008',
      price: 39.99,
      comparePrice: 55.00,
      categoryId: sports.id,
      quantity: 35,
      tags: 'yoga,mat,premium,non-slip',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop',
          altText: 'Yoga Mat Premium',
          sortOrder: 0,
        },
      ],
    },
    // Home & Garden
    {
      name: 'Smart LED Light Bulb',
      slug: 'smart-led-light-bulb',
      description: 'WiFi-enabled smart LED bulb with color changing and dimming capabilities.',
      shortDescription: 'WiFi-enabled smart LED bulb',
      sku: 'SLB-009',
      price: 24.99,
      comparePrice: 35.00,
      categoryId: home.id,
      quantity: 80,
      tags: 'smart,led,bulb,wifi,color',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
          altText: 'Smart LED Light Bulb',
          sortOrder: 0,
        },
      ],
    },
    {
      name: 'Ceramic Plant Pot Set',
      slug: 'ceramic-plant-pot-set',
      description: 'Beautiful set of 3 ceramic plant pots with drainage holes and saucers.',
      shortDescription: 'Set of 3 ceramic plant pots',
      sku: 'CPS-010',
      price: 45.99,
      comparePrice: 65.00,
      categoryId: home.id,
      quantity: 20,
      tags: 'ceramic,plant,pot,set,drainage',
      images: [
        {
          url: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400&h=400&fit=crop',
          altText: 'Ceramic Plant Pot Set',
          sortOrder: 0,
        },
      ],
    },
  ]

  for (const productData of products) {
    const { images, ...product } = productData
    const createdProduct = await prisma.product.create({
      data: product,
    })

    // Create product images
    for (const image of images) {
      await prisma.productImage.create({
        data: {
          ...image,
          productId: createdProduct.id,
        },
      })
    }
  }

  console.log('Seed data created successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
