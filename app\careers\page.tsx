"use client"

import { useState } from "react"
import { Breadcrumb } from "@/components/ui/breadcrumb"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileUpload } from "@/components/ui/file-upload"
import { MapPin, Clock, Users, Briefcase } from "lucide-react"

export default function CareersPage() {
  const [showResumeUpload, setShowResumeUpload] = useState(false)
  const jobOpenings = [
    {
      id: 1,
      title: "Senior Frontend Developer",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
      description: "Join our team to build amazing user experiences with React and Next.js",
      requirements: ["5+ years React experience", "TypeScript proficiency", "UI/UX design sense"],
    },
    {
      id: 2,
      title: "AI/ML Engineer",
      department: "Engineering",
      location: "San Francisco, CA",
      type: "Full-time",
      description: "Help us build the next generation of AI-powered shopping experiences",
      requirements: ["Python/TensorFlow experience", "Machine Learning background", "NLP knowledge"],
    },
    {
      id: 3,
      title: "Product Manager",
      department: "Product",
      location: "New York, NY",
      type: "Full-time",
      description: "Lead product strategy and roadmap for our ecommerce platform",
      requirements: ["5+ years product management", "Ecommerce experience", "Data-driven mindset"],
    },
    {
      id: 4,
      title: "Customer Success Manager",
      department: "Customer Success",
      location: "Remote",
      type: "Full-time",
      description: "Ensure our customers achieve success with our platform",
      requirements: ["Customer success experience", "Excellent communication", "Problem-solving skills"],
    },
  ]

  return (
    <div className="container px-4 py-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "Careers", href: "/careers" },
        ]}
      />

      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">Join Our Team</h1>
          <p className="text-xl text-muted-foreground">
            Help us build the future of AI-powered ecommerce
          </p>
        </div>

        {/* Company Culture */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="text-center">
            <CardHeader>
              <Users className="h-12 w-12 mx-auto text-blue-600 mb-4" />
              <CardTitle>Collaborative Culture</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Work with talented people who are passionate about creating amazing products
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Briefcase className="h-12 w-12 mx-auto text-green-600 mb-4" />
              <CardTitle>Growth Opportunities</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Continuous learning and development with mentorship and training programs
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Clock className="h-12 w-12 mx-auto text-purple-600 mb-4" />
              <CardTitle>Work-Life Balance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Flexible working hours and remote-friendly policies for better work-life balance
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Benefits */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-2xl">Why Work With Us?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3">Benefits & Perks</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Competitive salary and equity packages</li>
                  <li>• Comprehensive health, dental, and vision insurance</li>
                  <li>• 401(k) with company matching</li>
                  <li>• Unlimited PTO policy</li>
                  <li>• $2,000 annual learning & development budget</li>
                  <li>• Top-tier equipment and home office setup</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3">Our Values</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Customer obsession</li>
                  <li>• Innovation and experimentation</li>
                  <li>• Transparency and open communication</li>
                  <li>• Diversity and inclusion</li>
                  <li>• Continuous learning and growth</li>
                  <li>• Sustainable and ethical practices</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Job Openings */}
        <div>
          <h2 className="text-2xl font-bold mb-6">Open Positions</h2>
          <div className="space-y-6">
            {jobOpenings.map((job) => (
              <Card key={job.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl">{job.title}</CardTitle>
                      <div className="flex items-center gap-4 mt-2">
                        <Badge variant="secondary">{job.department}</Badge>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <MapPin className="h-4 w-4 mr-1" />
                          {job.location}
                        </div>
                        <Badge variant="outline">{job.type}</Badge>
                      </div>
                    </div>
                    <Button>Apply Now</Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{job.description}</p>
                  <div>
                    <h4 className="font-semibold mb-2">Requirements:</h4>
                    <ul className="list-disc list-inside text-sm text-muted-foreground">
                      {job.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Contact */}
        <Card className="mt-12">
          <CardHeader>
            <CardTitle>Don't See a Perfect Match?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              We're always looking for talented individuals to join our team. Send us your resume 
              and tell us how you'd like to contribute to our mission.
            </p>
            <Button
              variant="outline"
              onClick={() => setShowResumeUpload(!showResumeUpload)}
            >
              {showResumeUpload ? "Hide Upload Form" : "Send Us Your Resume"}
            </Button>
          </CardContent>
        </Card>

        {/* Resume Upload Form */}
        {showResumeUpload && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Submit Your Resume</CardTitle>
            </CardHeader>
            <CardContent>
              <FileUpload
                title="Upload Your Resume"
                description="Upload your resume in PDF, DOC, DOCX, or TXT format"
                acceptedTypes={[".pdf", ".doc", ".docx", ".txt"]}
                maxSize={5}
                onFileSelect={(file) => {
                  console.log("Resume uploaded:", file.name)
                  // In a real app, this would upload to server/database
                }}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
