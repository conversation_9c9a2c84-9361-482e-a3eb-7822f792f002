import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { z } from "zod"

const productQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("12"),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["name", "price", "createdAt", "rating"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  minPrice: z.string().optional(),
  maxPrice: z.string().optional(),
})

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const query = productQuerySchema.parse(Object.fromEntries(searchParams))

    // Mock data for now since database might not be ready
    const mockProducts = [
      {
        id: "1",
        name: "Wireless Bluetooth Headphones",
        slug: "wireless-bluetooth-headphones",
        description: "Premium wireless headphones with noise cancellation",
        price: 99.99,
        comparePrice: 149.99,
        image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.5,
        reviewCount: 128,
        isFeatured: true,
        tags: "wireless,bluetooth,headphones,audio",
      },
      {
        id: "2",
        name: "Smart Fitness Watch",
        slug: "smart-fitness-watch",
        description: "Advanced fitness tracking smartwatch",
        price: 199.99,
        comparePrice: 299.99,
        image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.8,
        reviewCount: 89,
        isFeatured: true,
        tags: "smartwatch,fitness,health,gps",
      },
      {
        id: "3",
        name: "Blue Running Sneakers",
        slug: "blue-running-sneakers",
        description: "Comfortable blue running sneakers",
        price: 89.99,
        comparePrice: 120.00,
        image: "https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.3,
        reviewCount: 56,
        isFeatured: true,
        tags: "sneakers,blue,running,shoes,sports",
      },
      {
        id: "4",
        name: "Classic Denim Jacket",
        slug: "classic-denim-jacket",
        description: "Timeless denim jacket",
        price: 79.99,
        comparePrice: 110.00,
        image: "https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.6,
        reviewCount: 234,
        isFeatured: false,
        tags: "denim,jacket,classic,cotton",
      },
      {
        id: "5",
        name: "Professional Basketball",
        slug: "professional-basketball",
        description: "Official size basketball",
        price: 34.99,
        comparePrice: 45.00,
        image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.4,
        reviewCount: 92,
        isFeatured: false,
        tags: "basketball,sports,official,grip",
      },
      {
        id: "6",
        name: "Smart LED Light Bulb",
        slug: "smart-led-light-bulb",
        description: "WiFi-enabled smart LED bulb",
        price: 24.99,
        comparePrice: 35.00,
        image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.2,
        reviewCount: 67,
        isFeatured: false,
        tags: "smart,led,bulb,wifi,color",
      },
    ]

    let filteredProducts = [...mockProducts]

    // Apply filters
    if (query.category) {
      filteredProducts = filteredProducts.filter(p => p.category.slug === query.category)
    }

    if (query.search) {
      const searchLower = query.search.toLowerCase()
      filteredProducts = filteredProducts.filter(p =>
        p.name.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower) ||
        p.tags.toLowerCase().includes(searchLower)
      )
    }

    if (query.minPrice) {
      filteredProducts = filteredProducts.filter(p => p.price >= parseFloat(query.minPrice!))
    }

    if (query.maxPrice) {
      filteredProducts = filteredProducts.filter(p => p.price <= parseFloat(query.maxPrice!))
    }

    // Apply sorting
    if (query.sortBy === "price") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.price - b.price : b.price - a.price
      )
    } else if (query.sortBy === "name") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
      )
    } else if (query.sortBy === "rating") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.rating - b.rating : b.rating - a.rating
      )
    }

    // Apply pagination
    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const skip = (page - 1) * limit
    const total = filteredProducts.length
    const paginatedProducts = filteredProducts.slice(skip, skip + limit)

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    console.error("Products API error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    // This would be protected by admin middleware in a real app
    const body = await req.json()
    
    const product = await db.product.create({
      data: {
        ...body,
        slug: body.slug || body.name.toLowerCase().replace(/\s+/g, "-"),
      },
      include: {
        category: true,
        images: true,
      },
    })

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error("Create product error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
