import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { z } from "zod"

const productQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("12"),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(["name", "price", "createdAt", "rating"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
  minPrice: z.string().optional(),
  maxPrice: z.string().optional(),
  brand: z.string().optional(),
})

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const query = productQuerySchema.parse(Object.fromEntries(searchParams))

    // Comprehensive mock data with 10+ products per category
    const mockProducts = [
      // ELECTRONICS (12 products)
      {
        id: "1",
        name: "Wireless Bluetooth Headphones",
        slug: "wireless-bluetooth-headphones",
        description: "Premium wireless headphones with noise cancellation",
        price: 99.99,
        comparePrice: 149.99,
        image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.5,
        reviewCount: 128,
        isFeatured: true,
        tags: "wireless,bluetooth,headphones,audio,bose",
      },
      {
        id: "2",
        name: "Smart Fitness Watch",
        slug: "smart-fitness-watch",
        description: "Advanced fitness tracking smartwatch",
        price: 199.99,
        comparePrice: 299.99,
        image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.8,
        reviewCount: 89,
        isFeatured: true,
        tags: "smartwatch,fitness,health,gps,apple",
      },
      {
        id: "7",
        name: "iPhone 15 Pro",
        slug: "iphone-15-pro",
        description: "Latest iPhone with advanced camera system",
        price: 999.99,
        comparePrice: 1099.99,
        image: "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.9,
        reviewCount: 456,
        isFeatured: true,
        tags: "smartphone,iphone,apple,camera,5g",
      },
      {
        id: "8",
        name: "Samsung Galaxy S24",
        slug: "samsung-galaxy-s24",
        description: "Flagship Android smartphone with AI features",
        price: 899.99,
        comparePrice: 999.99,
        image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.7,
        reviewCount: 324,
        isFeatured: true,
        tags: "smartphone,samsung,android,camera,ai",
      },
      {
        id: "9",
        name: "MacBook Air M3",
        slug: "macbook-air-m3",
        description: "Ultra-thin laptop with M3 chip",
        price: 1299.99,
        comparePrice: 1399.99,
        image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.8,
        reviewCount: 234,
        isFeatured: true,
        tags: "laptop,macbook,apple,m3,ultrabook",
      },
      {
        id: "10",
        name: "Sony WH-1000XM5",
        slug: "sony-wh-1000xm5",
        description: "Industry-leading noise canceling headphones",
        price: 349.99,
        comparePrice: 399.99,
        image: "https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.6,
        reviewCount: 567,
        isFeatured: false,
        tags: "headphones,sony,noise-canceling,wireless",
      },
      {
        id: "11",
        name: "iPad Pro 12.9",
        slug: "ipad-pro-12-9",
        description: "Professional tablet with M2 chip",
        price: 1099.99,
        comparePrice: 1199.99,
        image: "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.7,
        reviewCount: 189,
        isFeatured: false,
        tags: "tablet,ipad,apple,m2,professional",
      },
      {
        id: "12",
        name: "Nintendo Switch OLED",
        slug: "nintendo-switch-oled",
        description: "Gaming console with vibrant OLED screen",
        price: 349.99,
        comparePrice: 379.99,
        image: "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.5,
        reviewCount: 445,
        isFeatured: false,
        tags: "gaming,nintendo,console,oled,portable",
      },
      {
        id: "13",
        name: "LG 55 4K OLED TV",
        slug: "lg-55-4k-oled-tv",
        description: "Premium 4K OLED smart TV",
        price: 1499.99,
        comparePrice: 1799.99,
        image: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.6,
        reviewCount: 123,
        isFeatured: false,
        tags: "tv,oled,4k,smart,lg",
      },
      {
        id: "14",
        name: "AirPods Pro 2",
        slug: "airpods-pro-2",
        description: "Wireless earbuds with adaptive transparency",
        price: 249.99,
        comparePrice: 279.99,
        image: "https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.4,
        reviewCount: 678,
        isFeatured: false,
        tags: "earbuds,airpods,apple,wireless,noise-canceling",
      },
      {
        id: "15",
        name: "Canon EOS R5",
        slug: "canon-eos-r5",
        description: "Professional mirrorless camera",
        price: 3899.99,
        comparePrice: 4199.99,
        image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.8,
        reviewCount: 89,
        isFeatured: false,
        tags: "camera,canon,mirrorless,professional,photography",
      },
      {
        id: "16",
        name: "Tesla Model S Plaid",
        slug: "tesla-model-s-plaid",
        description: "Electric vehicle with autopilot",
        price: 89999.99,
        comparePrice: 94999.99,
        image: "https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=400&fit=crop",
        category: { id: "electronics", name: "Electronics", slug: "electronics" },
        rating: 4.9,
        reviewCount: 45,
        isFeatured: true,
        tags: "electric,car,tesla,autopilot,luxury",
      },

      // FASHION (12 products)
      {
        id: "3",
        name: "Blue Running Sneakers",
        slug: "blue-running-sneakers",
        description: "Comfortable blue running sneakers",
        price: 89.99,
        comparePrice: 120.00,
        image: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.3,
        reviewCount: 56,
        isFeatured: true,
        tags: "sneakers,blue,running,shoes,sports,nike",
      },
      {
        id: "4",
        name: "Classic Denim Jacket",
        slug: "classic-denim-jacket",
        description: "Timeless denim jacket",
        price: 79.99,
        comparePrice: 110.00,
        image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.6,
        reviewCount: 234,
        isFeatured: false,
        tags: "denim,jacket,classic,cotton,levis",
      },
      {
        id: "17",
        name: "Nike Air Max 270",
        slug: "nike-air-max-270",
        description: "Iconic lifestyle sneakers with Air Max cushioning",
        price: 129.99,
        comparePrice: 150.00,
        image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.5,
        reviewCount: 789,
        isFeatured: true,
        tags: "sneakers,nike,air-max,lifestyle,cushioning",
      },
      {
        id: "18",
        name: "Adidas Ultraboost 22",
        slug: "adidas-ultraboost-22",
        description: "Premium running shoes with Boost technology",
        price: 179.99,
        comparePrice: 200.00,
        image: "https://images.unsplash.com/photo-1608231387042-66d1773070a5?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.7,
        reviewCount: 456,
        isFeatured: true,
        tags: "sneakers,adidas,ultraboost,running,boost",
      },
      {
        id: "19",
        name: "Levi's 501 Original Jeans",
        slug: "levis-501-original-jeans",
        description: "Classic straight-leg jeans",
        price: 69.99,
        comparePrice: 89.99,
        image: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.4,
        reviewCount: 1234,
        isFeatured: false,
        tags: "jeans,levis,501,classic,denim",
      },
      {
        id: "20",
        name: "Zara Wool Coat",
        slug: "zara-wool-coat",
        description: "Elegant wool coat for winter",
        price: 199.99,
        comparePrice: 249.99,
        image: "https://images.unsplash.com/photo-1539533018447-63fcce2678e3?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.2,
        reviewCount: 167,
        isFeatured: false,
        tags: "coat,wool,winter,elegant,zara",
      },
      {
        id: "21",
        name: "H&M Cotton T-Shirt",
        slug: "hm-cotton-t-shirt",
        description: "Basic cotton t-shirt in multiple colors",
        price: 12.99,
        comparePrice: 19.99,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.0,
        reviewCount: 2345,
        isFeatured: false,
        tags: "t-shirt,cotton,basic,casual,hm",
      },
      {
        id: "22",
        name: "Nike Dri-FIT Shorts",
        slug: "nike-dri-fit-shorts",
        description: "Athletic shorts with moisture-wicking technology",
        price: 34.99,
        comparePrice: 45.00,
        image: "https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.3,
        reviewCount: 567,
        isFeatured: false,
        tags: "shorts,athletic,nike,dri-fit,sports",
      },
      {
        id: "23",
        name: "Adidas Track Jacket",
        slug: "adidas-track-jacket",
        description: "Classic three-stripe track jacket",
        price: 79.99,
        comparePrice: 99.99,
        image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.5,
        reviewCount: 234,
        isFeatured: false,
        tags: "jacket,track,adidas,three-stripe,athletic",
      },
      {
        id: "24",
        name: "Zara Leather Handbag",
        slug: "zara-leather-handbag",
        description: "Premium leather handbag with gold hardware",
        price: 149.99,
        comparePrice: 189.99,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.6,
        reviewCount: 123,
        isFeatured: true,
        tags: "handbag,leather,premium,zara,accessories",
      },
      {
        id: "25",
        name: "Levi's Trucker Jacket",
        slug: "levis-trucker-jacket",
        description: "Iconic denim trucker jacket",
        price: 89.99,
        comparePrice: 119.99,
        image: "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.7,
        reviewCount: 345,
        isFeatured: false,
        tags: "jacket,denim,trucker,levis,classic",
      },
      {
        id: "26",
        name: "H&M Summer Dress",
        slug: "hm-summer-dress",
        description: "Flowy summer dress in floral print",
        price: 39.99,
        comparePrice: 59.99,
        image: "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop",
        category: { id: "fashion", name: "Fashion", slug: "fashion" },
        rating: 4.1,
        reviewCount: 456,
        isFeatured: false,
        tags: "dress,summer,floral,casual,hm",
      },

      // SPORTS & FITNESS (12 products)
      {
        id: "5",
        name: "Professional Basketball",
        slug: "professional-basketball",
        description: "Official size basketball",
        price: 34.99,
        comparePrice: 45.00,
        image: "https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.4,
        reviewCount: 92,
        isFeatured: false,
        tags: "basketball,sports,official,grip,nike",
      },
      {
        id: "27",
        name: "Yoga Mat Premium",
        slug: "yoga-mat-premium",
        description: "Non-slip premium yoga mat",
        price: 39.99,
        comparePrice: 55.00,
        image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.2,
        reviewCount: 234,
        isFeatured: false,
        tags: "yoga,mat,premium,non-slip,fitness",
      },
      {
        id: "28",
        name: "Nike Training Shoes",
        slug: "nike-training-shoes",
        description: "Cross-training shoes for gym workouts",
        price: 119.99,
        comparePrice: 140.00,
        image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.6,
        reviewCount: 456,
        isFeatured: true,
        tags: "training,shoes,nike,gym,cross-training",
      },
      {
        id: "29",
        name: "Adidas Soccer Ball",
        slug: "adidas-soccer-ball",
        description: "FIFA-approved soccer ball",
        price: 29.99,
        comparePrice: 39.99,
        image: "https://images.unsplash.com/photo-1614632537190-23e4b2e69c88?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.5,
        reviewCount: 123,
        isFeatured: false,
        tags: "soccer,ball,adidas,fifa,football",
      },
      {
        id: "30",
        name: "Under Armour Gym Bag",
        slug: "under-armour-gym-bag",
        description: "Durable gym bag with multiple compartments",
        price: 49.99,
        comparePrice: 69.99,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.3,
        reviewCount: 234,
        isFeatured: false,
        tags: "gym,bag,under-armour,durable,sports",
      },
      {
        id: "31",
        name: "Puma Running Shorts",
        slug: "puma-running-shorts",
        description: "Lightweight running shorts with built-in brief",
        price: 24.99,
        comparePrice: 34.99,
        image: "https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.1,
        reviewCount: 345,
        isFeatured: false,
        tags: "running,shorts,puma,lightweight,athletic",
      },
      {
        id: "32",
        name: "Reebok Dumbbells Set",
        slug: "reebok-dumbbells-set",
        description: "Adjustable dumbbells for home workouts",
        price: 199.99,
        comparePrice: 249.99,
        image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.7,
        reviewCount: 89,
        isFeatured: true,
        tags: "dumbbells,weights,reebok,adjustable,home-gym",
      },
      {
        id: "33",
        name: "Nike Swim Goggles",
        slug: "nike-swim-goggles",
        description: "Anti-fog swimming goggles",
        price: 19.99,
        comparePrice: 29.99,
        image: "https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.0,
        reviewCount: 167,
        isFeatured: false,
        tags: "swimming,goggles,nike,anti-fog,water-sports",
      },
      {
        id: "34",
        name: "Adidas Tennis Racket",
        slug: "adidas-tennis-racket",
        description: "Professional tennis racket with carbon frame",
        price: 149.99,
        comparePrice: 189.99,
        image: "https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.4,
        reviewCount: 78,
        isFeatured: false,
        tags: "tennis,racket,adidas,professional,carbon",
      },
      {
        id: "35",
        name: "Under Armour Compression Shirt",
        slug: "under-armour-compression-shirt",
        description: "Moisture-wicking compression shirt",
        price: 39.99,
        comparePrice: 54.99,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.2,
        reviewCount: 234,
        isFeatured: false,
        tags: "compression,shirt,under-armour,moisture-wicking,athletic",
      },
      {
        id: "36",
        name: "Puma Football Cleats",
        slug: "puma-football-cleats",
        description: "High-performance football cleats",
        price: 89.99,
        comparePrice: 119.99,
        image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.5,
        reviewCount: 156,
        isFeatured: false,
        tags: "football,cleats,puma,performance,sports",
      },
      {
        id: "37",
        name: "Reebok Resistance Bands",
        slug: "reebok-resistance-bands",
        description: "Set of resistance bands for strength training",
        price: 29.99,
        comparePrice: 39.99,
        image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop",
        category: { id: "sports", name: "Sports & Fitness", slug: "sports" },
        rating: 4.3,
        reviewCount: 345,
        isFeatured: false,
        tags: "resistance,bands,reebok,strength,training",
      },

      // HOME & GARDEN (12 products)
      {
        id: "6",
        name: "Smart LED Light Bulb",
        slug: "smart-led-light-bulb",
        description: "WiFi-enabled smart LED bulb",
        price: 24.99,
        comparePrice: 35.00,
        image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.2,
        reviewCount: 67,
        isFeatured: false,
        tags: "smart,led,bulb,wifi,color,philips",
      },
      {
        id: "38",
        name: "IKEA Bookshelf",
        slug: "ikea-bookshelf",
        description: "Modern wooden bookshelf with 5 shelves",
        price: 79.99,
        comparePrice: 99.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.4,
        reviewCount: 234,
        isFeatured: false,
        tags: "bookshelf,furniture,ikea,wooden,storage",
      },
      {
        id: "39",
        name: "Philips Air Purifier",
        slug: "philips-air-purifier",
        description: "HEPA air purifier for large rooms",
        price: 299.99,
        comparePrice: 349.99,
        image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.6,
        reviewCount: 156,
        isFeatured: true,
        tags: "air-purifier,philips,hepa,clean-air,home",
      },
      {
        id: "40",
        name: "Wayfair Dining Table",
        slug: "wayfair-dining-table",
        description: "Solid wood dining table for 6 people",
        price: 599.99,
        comparePrice: 749.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.5,
        reviewCount: 89,
        isFeatured: true,
        tags: "dining-table,furniture,wayfair,wood,family",
      },
      {
        id: "41",
        name: "Pottery Barn Throw Pillows",
        slug: "pottery-barn-throw-pillows",
        description: "Set of 4 decorative throw pillows",
        price: 89.99,
        comparePrice: 119.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.3,
        reviewCount: 167,
        isFeatured: false,
        tags: "pillows,decorative,pottery-barn,home-decor,comfort",
      },
      {
        id: "42",
        name: "West Elm Coffee Table",
        slug: "west-elm-coffee-table",
        description: "Mid-century modern coffee table",
        price: 399.99,
        comparePrice: 499.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.4,
        reviewCount: 123,
        isFeatured: false,
        tags: "coffee-table,furniture,west-elm,mid-century,modern",
      },
      {
        id: "43",
        name: "IKEA Kitchen Cabinet",
        slug: "ikea-kitchen-cabinet",
        description: "Modular kitchen cabinet with soft-close doors",
        price: 149.99,
        comparePrice: 189.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.2,
        reviewCount: 345,
        isFeatured: false,
        tags: "kitchen,cabinet,ikea,modular,storage",
      },
      {
        id: "44",
        name: "Philips Smart Thermostat",
        slug: "philips-smart-thermostat",
        description: "WiFi-enabled programmable thermostat",
        price: 199.99,
        comparePrice: 249.99,
        image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.5,
        reviewCount: 234,
        isFeatured: true,
        tags: "thermostat,smart,philips,wifi,energy-saving",
      },
      {
        id: "45",
        name: "Wayfair Garden Hose",
        slug: "wayfair-garden-hose",
        description: "50ft expandable garden hose with spray nozzle",
        price: 39.99,
        comparePrice: 59.99,
        image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.1,
        reviewCount: 456,
        isFeatured: false,
        tags: "garden,hose,wayfair,expandable,outdoor",
      },
      {
        id: "46",
        name: "Pottery Barn Area Rug",
        slug: "pottery-barn-area-rug",
        description: "Hand-woven area rug in neutral colors",
        price: 299.99,
        comparePrice: 399.99,
        image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.6,
        reviewCount: 78,
        isFeatured: false,
        tags: "rug,area-rug,pottery-barn,hand-woven,home-decor",
      },
      {
        id: "47",
        name: "West Elm Floor Lamp",
        slug: "west-elm-floor-lamp",
        description: "Modern brass floor lamp with fabric shade",
        price: 199.99,
        comparePrice: 249.99,
        image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.3,
        reviewCount: 167,
        isFeatured: false,
        tags: "lamp,floor-lamp,west-elm,brass,lighting",
      },
      {
        id: "48",
        name: "IKEA Plant Pot Set",
        slug: "ikea-plant-pot-set",
        description: "Set of 3 ceramic plant pots with drainage",
        price: 29.99,
        comparePrice: 39.99,
        image: "https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400&h=400&fit=crop",
        category: { id: "home", name: "Home & Garden", slug: "home" },
        rating: 4.0,
        reviewCount: 567,
        isFeatured: false,
        tags: "plant-pot,ceramic,ikea,drainage,gardening",
      },
    ]

    let filteredProducts = [...mockProducts]

    // Apply filters
    if (query.category) {
      filteredProducts = filteredProducts.filter(p => p.category.slug === query.category)
    }

    if (query.search) {
      const searchLower = query.search.toLowerCase()
      filteredProducts = filteredProducts.filter(p =>
        p.name.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower) ||
        p.tags.toLowerCase().includes(searchLower)
      )
    }

    if (query.minPrice) {
      filteredProducts = filteredProducts.filter(p => p.price >= parseFloat(query.minPrice!))
    }

    if (query.maxPrice) {
      filteredProducts = filteredProducts.filter(p => p.price <= parseFloat(query.maxPrice!))
    }

    // Apply brand filter
    if (query.brand) {
      filteredProducts = filteredProducts.filter(p =>
        p.tags.toLowerCase().includes(query.brand!.toLowerCase())
      )
    }

    // Apply sorting
    if (query.sortBy === "price") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.price - b.price : b.price - a.price
      )
    } else if (query.sortBy === "name") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name)
      )
    } else if (query.sortBy === "rating") {
      filteredProducts.sort((a, b) =>
        query.sortOrder === "asc" ? a.rating - b.rating : b.rating - a.rating
      )
    }

    // Apply pagination
    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const skip = (page - 1) * limit
    const total = filteredProducts.length
    const paginatedProducts = filteredProducts.slice(skip, skip + limit)

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    console.error("Products API error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    // This would be protected by admin middleware in a real app
    const body = await req.json()
    
    const product = await db.product.create({
      data: {
        ...body,
        slug: body.slug || body.name.toLowerCase().replace(/\s+/g, "-"),
      },
      include: {
        category: true,
        images: true,
      },
    })

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error("Create product error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
