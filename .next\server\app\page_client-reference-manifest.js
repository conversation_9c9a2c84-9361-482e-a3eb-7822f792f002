globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/chatbot/chatbot.tsx":{"*":{"id":"(ssr)/./components/chatbot/chatbot.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/footer.tsx":{"*":{"id":"(ssr)/./components/layout/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/header.tsx":{"*":{"id":"(ssr)/./components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers.tsx":{"*":{"id":"(ssr)/./components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\ai-recommendations.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\category-showcase.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\featured-products.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\hero-section.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\newsletter-signup.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\trust-badges.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\chatbot\\chatbot.tsx":{"id":"(app-pages-browser)/./components/chatbot/chatbot.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\layout\\footer.tsx":{"id":"(app-pages-browser)/./components/layout/footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\layout\\header.tsx":{"id":"(app-pages-browser)/./components/layout/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\providers.tsx":{"id":"(app-pages-browser)/./components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\page":[],"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\not-found":[]}}