import { Breadcrumb } from "@/components/ui/breadcrumb"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Award, Globe, Zap } from "lucide-react"

export default function AboutPage() {
  return (
    <div className="container px-4 py-8">
      <Breadcrumb
        items={[
          { label: "Home", href: "/" },
          { label: "About", href: "/about" },
        ]}
      />

      <div className="max-w-4xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">About ECommerce</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            We're revolutionizing online shopping with AI-powered recommendations, 
            secure transactions, and exceptional customer service.
          </p>
        </div>

        {/* Mission Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">Our Mission</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg text-muted-foreground">
              To create the most intelligent and user-friendly ecommerce platform that connects 
              customers with the perfect products through cutting-edge AI technology, while 
              maintaining the highest standards of security and customer satisfaction.
            </p>
          </CardContent>
        </Card>

        {/* Values */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Zap className="h-6 w-6 text-blue-600" />
                <CardTitle>Innovation</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We leverage the latest AI and machine learning technologies to provide 
                personalized shopping experiences.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-green-600" />
                <CardTitle>Customer First</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Every decision we make is centered around creating the best possible 
                experience for our customers.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Award className="h-6 w-6 text-purple-600" />
                <CardTitle>Quality</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We partner with trusted brands and sellers to ensure every product 
                meets our high quality standards.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Globe className="h-6 w-6 text-orange-600" />
                <CardTitle>Global Reach</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Connecting customers worldwide with products from trusted sellers 
                across the globe.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">10K+</div>
            <div className="text-sm text-muted-foreground">Products</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">50K+</div>
            <div className="text-sm text-muted-foreground">Happy Customers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">99.9%</div>
            <div className="text-sm text-muted-foreground">Uptime</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-sm text-muted-foreground">Support</div>
          </div>
        </div>

        {/* Technology */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Our Technology</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">AI-Powered Recommendations</h3>
                <p className="text-muted-foreground">
                  Our machine learning algorithms analyze your preferences and behavior 
                  to suggest products you'll love.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Secure Infrastructure</h3>
                <p className="text-muted-foreground">
                  Built with enterprise-grade security, including SSL encryption, 
                  PCI DSS compliance, and GDPR protection.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Scalable Platform</h3>
                <p className="text-muted-foreground">
                  Our cloud-native architecture ensures fast loading times and 
                  reliable performance even during peak traffic.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
