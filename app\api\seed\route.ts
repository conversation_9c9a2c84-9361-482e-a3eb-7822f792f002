import { NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function POST() {
  try {
    // Check if data already exists
    const existingCategories = await db.category.count()
    if (existingCategories > 0) {
      return NextResponse.json({ message: 'Database already seeded!' })
    }

    // Create categories
    const electronics = await db.category.create({
      data: {
        name: 'Electronics',
        slug: 'electronics',
        description: 'Latest gadgets and tech',
        image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
      },
    })

    const fashion = await db.category.create({
      data: {
        name: 'Fashion',
        slug: 'fashion',
        description: 'Trendy clothing and accessories',
        image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
      },
    })

    const home = await db.category.create({
      data: {
        name: 'Home & Garden',
        slug: 'home',
        description: 'Everything for your home',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
      },
    })

    const sports = await db.category.create({
      data: {
        name: 'Sports & Fitness',
        slug: 'sports',
        description: 'Gear for active lifestyle',
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      },
    })

    // Create products
    const products = [
      // Electronics
      {
        name: 'Wireless Bluetooth Headphones',
        slug: 'wireless-bluetooth-headphones',
        description: 'High-quality wireless headphones with noise cancellation and long battery life.',
        shortDescription: 'Premium wireless headphones with noise cancellation',
        sku: 'WBH-001',
        price: 99.99,
        comparePrice: 149.99,
        categoryId: electronics.id,
        quantity: 50,
        isFeatured: true,
        tags: 'wireless,bluetooth,headphones,audio',
        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
      },
      {
        name: 'Smart Fitness Watch',
        slug: 'smart-fitness-watch',
        description: 'Advanced fitness tracking with heart rate monitoring, GPS, and smartphone integration.',
        shortDescription: 'Advanced fitness tracking smartwatch',
        sku: 'SFW-002',
        price: 199.99,
        comparePrice: 299.99,
        categoryId: electronics.id,
        quantity: 30,
        isFeatured: true,
        tags: 'smartwatch,fitness,health,gps',
        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      },
      {
        name: 'Blue Running Sneakers',
        slug: 'blue-running-sneakers',
        description: 'Comfortable blue running sneakers with advanced cushioning and breathable mesh.',
        shortDescription: 'Comfortable blue running sneakers',
        sku: 'BRS-005',
        price: 89.99,
        comparePrice: 120.00,
        categoryId: fashion.id,
        quantity: 40,
        isFeatured: true,
        tags: 'sneakers,blue,running,shoes,sports',
        image: 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop',
      },
      {
        name: 'Classic Denim Jacket',
        slug: 'classic-denim-jacket',
        description: 'Timeless denim jacket made from premium cotton with a comfortable fit.',
        shortDescription: 'Timeless denim jacket',
        sku: 'CDJ-006',
        price: 79.99,
        comparePrice: 110.00,
        categoryId: fashion.id,
        quantity: 25,
        tags: 'denim,jacket,classic,cotton',
        image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',
      },
      {
        name: 'Professional Basketball',
        slug: 'professional-basketball',
        description: 'Official size basketball with superior grip and durability for indoor and outdoor play.',
        shortDescription: 'Official size basketball',
        sku: 'PBB-007',
        price: 34.99,
        comparePrice: 45.00,
        categoryId: sports.id,
        quantity: 60,
        tags: 'basketball,sports,official,grip',
        image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop',
      },
      {
        name: 'Smart LED Light Bulb',
        slug: 'smart-led-light-bulb',
        description: 'WiFi-enabled smart LED bulb with color changing and dimming capabilities.',
        shortDescription: 'WiFi-enabled smart LED bulb',
        sku: 'SLB-009',
        price: 24.99,
        comparePrice: 35.00,
        categoryId: home.id,
        quantity: 80,
        tags: 'smart,led,bulb,wifi,color',
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
      },
    ]

    for (const productData of products) {
      const { image, ...product } = productData
      const createdProduct = await db.product.create({
        data: product,
      })

      // Create product image
      await db.productImage.create({
        data: {
          url: image,
          altText: product.name,
          sortOrder: 0,
          productId: createdProduct.id,
        },
      })
    }

    return NextResponse.json({ message: 'Database seeded successfully!' })
  } catch (error) {
    console.error('Seed error:', error)
    return NextResponse.json(
      { message: 'Failed to seed database', error: error.message },
      { status: 500 }
    )
  }
}
