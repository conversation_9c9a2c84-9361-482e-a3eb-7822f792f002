"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chatbot/route.ts */ \"(rsc)/./app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chatbot/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjaGF0Ym90JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjaGF0Ym90JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY2hhdGJvdCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNPbWthciU1Q0RvY3VtZW50cyU1Q09ta2FyJTVDc3RhcnQlNUNwcm9qZWN0cyU1Q21vY2slNUNlY29tJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNPbWthciU1Q0RvY3VtZW50cyU1Q09ta2FyJTVDc3RhcnQlNUNwcm9qZWN0cyU1Q21vY2slNUNlY29tJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQzRDO0FBQ3pIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDNko7O0FBRTdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWNvbW1lcmNlLWFwcC8/YmE0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxPbWthclxcXFxEb2N1bWVudHNcXFxcT21rYXJcXFxcc3RhcnRcXFxccHJvamVjdHNcXFxcbW9ja1xcXFxlY29tXFxcXGFwcFxcXFxhcGlcXFxcY2hhdGJvdFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY2hhdGJvdC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NoYXRib3RcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NoYXRib3Qvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxPbWthclxcXFxEb2N1bWVudHNcXFxcT21rYXJcXFxcc3RhcnRcXFxccHJvamVjdHNcXFxcbW9ja1xcXFxlY29tXFxcXGFwcFxcXFxhcGlcXFxcY2hhdGJvdFxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9jaGF0Ym90L3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCwgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/chatbot/route.ts":
/*!**********************************!*\
  !*** ./app/api/chatbot/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY || \"\");\n// Product database for context\nconst PRODUCT_CONTEXT = `\nYou are an AI shopping assistant for an ecommerce platform. Here are our current products:\n\nELECTRONICS (12 products):\n- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation\n- Smart Fitness Watch ($199.99) - Advanced fitness tracking\n- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera\n- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features\n- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip\n- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling\n- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip\n- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen\n- LG 55\" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV\n- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency\n- Canon EOS R5 ($3899.99) - Professional mirrorless camera\n- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot\n\nFASHION (12 products):\n- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning\n- Classic Denim Jacket ($79.99) - Timeless denim jacket\n- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers\n- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost\n- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans\n- Zara Wool Coat ($199.99) - Elegant wool coat for winter\n- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors\n- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking\n- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket\n- Zara Leather Handbag ($149.99) - Premium leather with gold hardware\n- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket\n- H&M Summer Dress ($39.99) - Flowy summer dress in floral print\n\nSPORTS & FITNESS (12 products):\n- Professional Basketball ($34.99) - Official size basketball\n- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat\n- Nike Training Shoes ($119.99) - Cross-training shoes for gym\n- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball\n- Under Armour Gym Bag ($49.99) - Durable with multiple compartments\n- Puma Running Shorts ($24.99) - Lightweight with built-in brief\n- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts\n- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles\n- Adidas Tennis Racket ($149.99) - Professional with carbon frame\n- Under Armour Compression Shirt ($39.99) - Moisture-wicking\n- Puma Football Cleats ($89.99) - High-performance football cleats\n- Reebok Resistance Bands ($29.99) - Set for strength training\n\nHOME & GARDEN (12 products):\n- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb\n- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves\n- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms\n- Wayfair Dining Table ($599.99) - Solid wood dining table for 6\n- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows\n- West Elm Coffee Table ($399.99) - Mid-century modern coffee table\n- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors\n- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable\n- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle\n- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors\n- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade\n- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage\n\nSTORE POLICIES:\n- Free shipping on orders over $50\n- 30-day return policy\n- Standard shipping: 3-5 business days ($5.99)\n- Express shipping: 1-2 business days ($12.99)\n- International shipping available\n- We accept all major credit cards, PayPal, Apple Pay, Google Pay\n- Customer service: +****************\n- Email: <EMAIL>\n`;\nasync function POST(req) {\n    try {\n        const { message, sessionId } = await req.json();\n        if (!message || !sessionId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Message and sessionId are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if Gemini API key is available\n        if (!process.env.GEMINI_API_KEY) {\n            console.warn(\"GEMINI_API_KEY not found, using intelligent fallback responses\");\n            return getIntelligentFallbackResponse(message, sessionId);\n        }\n        try {\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-pro\"\n            });\n            const prompt = `${PRODUCT_CONTEXT}\n\nYou are a friendly, enthusiastic AI shopping assistant named Alex. You LOVE helping people find products and you're genuinely excited about shopping. The customer says: \"${message}\"\n\nPERSONALITY GUIDELINES:\n- Be conversational, warm, and enthusiastic (like talking to a friend who loves shopping)\n- Use natural language, contractions, and casual expressions\n- Show genuine interest in helping them find the perfect product\n- Use emojis naturally but not excessively (1-2 per response)\n- Ask follow-up questions to understand their needs better\n- Share personal opinions about products (\"I love this one!\" \"This is super popular!\")\n- Be encouraging and positive\n\nRESPONSE GUIDELINES:\n1. If they ask for a product we don't have, acknowledge it honestly and suggest great alternatives\n2. When recommending products, explain WHY they're good choices\n3. Ask about their specific needs (budget, use case, preferences)\n4. Keep responses conversational but informative (3-5 sentences)\n5. Always include specific product names and prices from our inventory\n6. Guide them naturally toward making a decision\n\nAVOID:\n- Robotic or formal language\n- Long lists without context\n- Generic responses\n- Being pushy or sales-y\n\nRespond as Alex, the enthusiastic shopping assistant who genuinely wants to help them find something perfect:`;\n            const result = await model.generateContent(prompt);\n            const response = result.response.text();\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                response,\n                sessionId\n            });\n        } catch (aiError) {\n            console.error(\"Gemini AI error:\", aiError);\n            // Fallback to predefined responses if AI fails\n            return getIntelligentFallbackResponse(message, sessionId);\n        }\n    } catch (error) {\n        console.error(\"Chatbot API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Intelligent fallback function for when AI is not available\nfunction getIntelligentFallbackResponse(message, sessionId) {\n    const lowerMessage = message.toLowerCase();\n    let response = \"\";\n    // Greeting responses\n    if (lowerMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {\n        const greetings = [\n            \"Hi there! \\uD83D\\uDE0A I'm here to help you find exactly what you're looking for. What's on your shopping list today?\",\n            \"Hello! Great to see you! \\uD83D\\uDC4B I'd love to help you discover some amazing products. What can I help you find?\",\n            \"Hey! Welcome! I'm excited to help you shop. Are you looking for something specific today?\",\n            \"Hi! I'm your personal shopping assistant. What brings you here today - anything particular you need?\"\n        ];\n        response = greetings[Math.floor(Math.random() * greetings.length)];\n    } else if (lowerMessage.includes(\"hoodie\") || lowerMessage.includes(\"sweatshirt\")) {\n        response = `I'd love to help you find a hoodie! 😊\n\nUnfortunately, I don't see any hoodies in our current collection, but I do have some great alternatives that might work:\n\n• **Adidas Track Jacket** - $79.99\n  Classic three-stripe design, perfect for casual wear\n\n• **Zara Wool Coat** - $199.99\n  Cozy and warm for cooler weather\n\n• **Classic Denim Jacket** - $79.99\n  Timeless style that goes with everything\n\nWould any of these work for you, or are you specifically looking for a pullover hoodie? I can also check if we're getting any hoodies in stock soon! 🤔`;\n    } else if (lowerMessage.includes(\"jacket\") && !lowerMessage.includes(\"denim\")) {\n        response = `Great choice! I have some fantastic jackets for you:\n\n• **Adidas Track Jacket** - $79.99 ⭐\n  Classic athletic style with the iconic three stripes\n\n• **Classic Denim Jacket** - $79.99\n  Timeless denim that never goes out of style\n\n• **Zara Wool Coat** - $199.99\n  Perfect for colder weather, very elegant\n\nWhat style are you going for - sporty, casual, or more formal? That'll help me point you to the perfect one! 😊`;\n    } else if (lowerMessage.includes(\"t-shirt\") || lowerMessage.includes(\"tshirt\") || lowerMessage.includes(\"shirt\")) {\n        response = `Perfect! I have just what you need:\n\n• **H&M Cotton T-Shirt** - $12.99 ✨\n  Super comfortable basic cotton tee, available in multiple colors\n\n• **Under Armour Compression Shirt** - $39.99\n  Great for workouts with moisture-wicking technology\n\nThe H&M cotton tee is really popular - it's soft, affordable, and comes in lots of colors. What color were you thinking? 🎨`;\n    } else if (lowerMessage.includes(\"under\") || lowerMessage.includes(\"below\") || lowerMessage.includes(\"less than\")) {\n        const priceMatch = lowerMessage.match(/\\$?(\\d+)/);\n        const budget = priceMatch ? parseInt(priceMatch[1]) : 100;\n        if (lowerMessage.includes(\"headphones\") || lowerMessage.includes(\"earbuds\")) {\n            if (budget >= 200) {\n                response = `Awesome! You've got a good budget for some really nice headphones! 🎧\n\nHere's what I'd recommend under $${budget}:\n\n• **Wireless Bluetooth Headphones** - $99.99 ⭐\n  These are fantastic! Premium sound with noise cancellation, and they're our bestseller\n\n• **AirPods Pro 2** - $249.99\n  If you're an Apple user, these are incredible with adaptive transparency\n\n• **Sony WH-1000XM5** - $349.99\n  A bit over budget but worth mentioning - industry-leading noise canceling\n\nHonestly, the Wireless Bluetooth ones at $99.99 are amazing value. Great sound quality and the noise cancellation really works! What do you think? 😊`;\n            } else {\n                response = `Perfect! I have exactly what you need under $${budget}:\n\n• **Wireless Bluetooth Headphones** - $99.99 🎧\n  These are seriously good! Premium wireless with noise cancellation\n\nThey're our most popular headphones and for good reason - great sound, comfortable for hours, and the noise cancellation is surprisingly effective for the price. Plus they're well within your budget!\n\nAre you planning to use them mainly for music, calls, or maybe working out? 🤔`;\n            }\n        } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\")) {\n            response = `Here are some great electronics under $${budget}:\n\n• **Smart LED Light Bulb** - $24.99\n  WiFi-enabled smart LED bulb with color changing\n\n• **Nike Swim Goggles** - $19.99\n  Anti-fog swimming goggles\n\n${budget >= 100 ? \"• **Wireless Bluetooth Headphones** - $99.99\\n  Premium wireless headphones with noise cancellation\" : \"\"}\n\nWhat type of electronics are you most interested in?`;\n        } else {\n            response = `I can help you find great products under $${budget}! Here are some popular options:\n\n• **H&M Cotton T-Shirt** - $12.99 (Fashion)\n• **Smart LED Light Bulb** - $24.99 (Home)\n• **Adidas Soccer Ball** - $29.99 (Sports)\n${budget >= 50 ? \"• **Under Armour Gym Bag** - $49.99 (Sports)\" : \"\"}\n${budget >= 90 ? \"• **Blue Running Sneakers** - $89.99 (Fashion)\" : \"\"}\n\nWhat category interests you most?`;\n        }\n    } else if (lowerMessage.includes(\"sneakers\") || lowerMessage.includes(\"shoes\")) {\n        response = `Oh nice! I love helping people find the perfect sneakers! 👟\n\nHere are some awesome options I think you'll love:\n\n• **Blue Running Sneakers** - $89.99 ⭐\n  These are super comfortable with amazing cushioning - great for running or just everyday wear\n\n• **Nike Air Max 270** - $129.99\n  Classic Nike style! The Air Max cushioning feels like walking on clouds\n\n• **Adidas Ultraboost 22** - $179.99\n  Premium choice with Boost technology - runners absolutely love these\n\nAre you looking for something more for running, casual wear, or maybe gym workouts? That'll help me narrow down the perfect pair for you! 😊`;\n    } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\") || lowerMessage.includes(\"gadgets\")) {\n        response = `Ooh, a tech lover! 🤓 I've got some really cool electronics that might catch your eye:\n\n🎧 **Audio stuff** (my personal favorites!)\n• **Wireless Bluetooth Headphones** - $99.99 - Amazing sound quality\n• **Sony WH-1000XM5** - $349.99 - The noise canceling is incredible\n• **AirPods Pro 2** - $249.99 - Perfect if you're in the Apple ecosystem\n\n📱 **Phones & Computers**\n• **iPhone 15 Pro** - $999.99 - Latest and greatest from Apple\n• **Samsung Galaxy S24** - $899.99 - Fantastic Android with AI features\n• **MacBook Air M3** - $1299.99 - Super thin and crazy fast\n\n🏠 **Smart Home** (these are fun!)\n• **Smart LED Light Bulb** - $24.99 - Changes colors with your phone\n• **Philips Smart Thermostat** - $199.99 - Saves money on energy bills\n\nWhat kind of tech are you in the mood for? Looking to upgrade something specific? 😊`;\n    } else if (lowerMessage.includes(\"fashion\") || lowerMessage.includes(\"clothing\") || lowerMessage.includes(\"clothes\")) {\n        response = `Fashion time! 👗 I love helping people put together great looks!\n\nHere's what's popular right now:\n\n👟 **Shoes** (always a good place to start!)\n• **Blue Running Sneakers** - $89.99 - Super versatile, goes with everything\n• **Nike Air Max 270** - $129.99 - Classic style that never gets old\n• **Adidas Ultraboost 22** - $179.99 - Sporty but stylish\n\n👕 **Clothing**\n• **H&M Cotton T-Shirt** - $12.99 - Perfect basic, comes in tons of colors\n• **Classic Denim Jacket** - $79.99 - Timeless piece, works with any outfit\n• **Zara Wool Coat** - $199.99 - So elegant and cozy for cooler weather\n\n👜 **Accessories**\n• **Zara Leather Handbag** - $149.99 - Beautiful quality, very chic\n\nWhat's your style vibe? Casual and comfy, or are you looking to dress up a bit? 😊`;\n    } else if (lowerMessage.includes(\"sports\") || lowerMessage.includes(\"fitness\") || lowerMessage.includes(\"gym\")) {\n        response = `Great choice! Here's our sports & fitness collection:\n\n🏀 **Sports Equipment**\n• **Professional Basketball** - $34.99\n• **Adidas Soccer Ball** - $29.99\n• **Adidas Tennis Racket** - $149.99\n\n💪 **Fitness Gear**\n• **Yoga Mat Premium** - $39.99\n• **Reebok Dumbbells Set** - $199.99\n• **Under Armour Gym Bag** - $49.99\n\n👟 **Athletic Wear**\n• **Nike Training Shoes** - $119.99\n• **Under Armour Compression Shirt** - $39.99\n\nWhat sport or activity are you training for?`;\n    } else if (lowerMessage.includes(\"home\") || lowerMessage.includes(\"furniture\") || lowerMessage.includes(\"garden\")) {\n        response = `Here are some great home & garden items:\n\n💡 **Smart Home**\n• **Smart LED Light Bulb** - $24.99\n• **Philips Air Purifier** - $299.99\n• **Philips Smart Thermostat** - $199.99\n\n🪑 **Furniture**\n• **IKEA Bookshelf** - $79.99\n• **Wayfair Dining Table** - $599.99\n• **West Elm Coffee Table** - $399.99\n\n🌱 **Garden**\n• **Wayfair Garden Hose** - $39.99\n• **IKEA Plant Pot Set** - $29.99\n\nWhat room are you looking to improve?`;\n    } else if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"support\")) {\n        response = `Of course! I'm here to help with whatever you need! 😊\n\nI can help you with:\n🛍️ **Finding products** - Just tell me what you're looking for\n💰 **Budget questions** - I'll find great options in your price range\n🚚 **Shipping info** - We have free shipping on orders over $50!\n💳 **Payment stuff** - We accept pretty much everything\n📞 **Other questions** - Call our team at +****************\n\nWhat's on your mind? I'm here to make your shopping super easy! 🛒`;\n    } else if (lowerMessage.includes(\"blue\")) {\n        response = `Looking for blue items? Here are some great options:\n\n• **Blue Running Sneakers** - $89.99 ⭐\n  Comfortable blue running sneakers with advanced cushioning\n\nPerfect for running or casual wear! Would you like to see more blue products?`;\n    } else if (lowerMessage.includes(\"cheap\") || lowerMessage.includes(\"affordable\") || lowerMessage.includes(\"budget\")) {\n        response = `Here are some great budget-friendly options:\n\n💰 **Under $30**\n• **H&M Cotton T-Shirt** - $12.99\n• **Nike Swim Goggles** - $19.99\n• **Smart LED Light Bulb** - $24.99\n• **Adidas Soccer Ball** - $29.99\n\n💰 **Under $50**\n• **Professional Basketball** - $34.99\n• **Yoga Mat Premium** - $39.99\n• **Under Armour Gym Bag** - $49.99\n\nWhat's your budget range?`;\n    } else {\n        // Handle unknown queries more naturally\n        const unknownResponses = [\n            `Hmm, I'm not sure I caught that exactly! 🤔 Could you tell me a bit more about what you're looking for? I've got electronics, fashion, sports gear, and home stuff!`,\n            `I want to help you find something great! Could you be a bit more specific? Are you shopping for yourself or maybe looking for a gift? 😊`,\n            `I'm here to help! What kind of product are you in the mood for today? Electronics, clothes, sports stuff, or maybe something for your home? 🛍️`,\n            `Let me help you find exactly what you need! What's on your shopping list today? I love helping people discover cool products! ✨`\n        ];\n        response = unknownResponses[Math.floor(Math.random() * unknownResponses.length)];\n    }\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        response,\n        sessionId\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chatbot/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();