"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chatbot/route.ts */ \"(rsc)/./app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chatbot/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/chatbot/route.ts":
/*!**********************************!*\
  !*** ./app/api/chatbot/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(req) {\n    try {\n        const { message, sessionId } = await req.json();\n        if (!message || !sessionId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Message and sessionId are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Simple AI logic for product recommendations\n        const lowerMessage = message.toLowerCase();\n        let response = \"\";\n        // Check for product searches\n        if (lowerMessage.includes(\"sneakers\") || lowerMessage.includes(\"shoes\")) {\n            // Mock sneakers data\n            const sneakers = [\n                {\n                    name: \"Blue Running Sneakers\",\n                    price: 89.99,\n                    description: \"Comfortable blue running sneakers with advanced cushioning\"\n                },\n                {\n                    name: \"Classic White Sneakers\",\n                    price: 79.99,\n                    description: \"Timeless white sneakers perfect for everyday wear\"\n                }\n            ];\n            response = `I found some great sneakers for you! Here are my top recommendations:\\n\\n`;\n            sneakers.forEach((sneaker, index)=>{\n                response += `${index + 1}. **${sneaker.name}** - $${sneaker.price}\\n`;\n                response += `   ${sneaker.description}\\n\\n`;\n            });\n            response += `Would you like to see more details about any of these products? You can also visit our products page to see the full collection!`;\n        } else if (lowerMessage.includes(\"blue\") && (lowerMessage.includes(\"sneakers\") || lowerMessage.includes(\"shoes\"))) {\n            // Mock blue sneakers data\n            const blueShoes = [\n                {\n                    name: \"Blue Running Sneakers\",\n                    price: 89.99,\n                    description: \"Comfortable blue running sneakers with advanced cushioning and breathable mesh\"\n                }\n            ];\n            response = `Perfect! I found some blue sneakers that might interest you:\\n\\n`;\n            blueShoes.forEach((shoe, index)=>{\n                response += `${index + 1}. **${shoe.name}** - $${shoe.price}\\n`;\n                response += `   ${shoe.description}\\n\\n`;\n            });\n            response += `These blue sneakers are popular choices! Would you like to know more about any specific pair?`;\n        } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"gadgets\") || lowerMessage.includes(\"tech\")) {\n            // Mock electronics data\n            const electronics = [\n                {\n                    name: \"Wireless Bluetooth Headphones\",\n                    price: 99.99,\n                    description: \"Premium wireless headphones with noise cancellation\"\n                },\n                {\n                    name: \"Smart Fitness Watch\",\n                    price: 199.99,\n                    description: \"Advanced fitness tracking smartwatch\"\n                },\n                {\n                    name: \"Smart LED Light Bulb\",\n                    price: 24.99,\n                    description: \"WiFi-enabled smart LED bulb\"\n                }\n            ];\n            response = `Here are some popular electronics from our collection:\\n\\n`;\n            electronics.forEach((item, index)=>{\n                response += `${index + 1}. **${item.name}** - $${item.price}\\n`;\n                response += `   ${item.description}\\n\\n`;\n            });\n            response += `These are some of our best-selling tech products! Interested in learning more about any of them?`;\n        } else if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"support\")) {\n            response = `I'm here to help! Here's what I can assist you with:\n\n🛍️ **Product Search** - Tell me what you're looking for and I'll find the best options\n📱 **Product Information** - Ask about specific products, prices, or features  \n🚚 **Shipping & Returns** - Get info about delivery and return policies\n💳 **Payment** - Learn about payment options and security\n🎯 **Recommendations** - Get personalized product suggestions\n\nWhat would you like help with today?`;\n        } else if (lowerMessage.includes(\"price\") || lowerMessage.includes(\"cost\") || lowerMessage.includes(\"cheap\") || lowerMessage.includes(\"affordable\")) {\n            response = `I can help you find products within your budget! Here are some ways to find great deals:\n\n💰 **Budget-Friendly Options** - Check our Deals page for discounts\n🏷️ **Sort by Price** - Use the price filter on our products page\n🔥 **Featured Deals** - Look for products marked as \"Featured\" for special offers\n📦 **Bundle Deals** - Some products offer better value when bought together\n\nWhat's your budget range? I can help you find the best options!`;\n        } else if (lowerMessage.includes(\"shipping\") || lowerMessage.includes(\"delivery\")) {\n            response = `Here's everything you need to know about shipping:\n\n🚚 **Free Shipping** - On orders over $50\n⚡ **Fast Delivery** - 2-3 business days for most items\n📦 **Tracking** - You'll get a tracking number once your order ships\n🌍 **Worldwide** - We ship to most countries\n\nNeed help with a specific order or have questions about delivery times?`;\n        } else {\n            // General response\n            response = `Hi there! I'm your AI shopping assistant. I can help you:\n\n🔍 Find specific products (just tell me what you're looking for!)\n💡 Get product recommendations based on your needs\n💰 Find the best deals and offers\n📋 Answer questions about products, shipping, and more\n\nWhat are you shopping for today? For example, you could say \"I'm looking for blue sneakers\" or \"Show me electronics under $100\"`;\n        }\n        // Note: In production, you would save the conversation to database\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            response\n        });\n    } catch (error) {\n        console.error(\"Chatbot API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chatbot/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();