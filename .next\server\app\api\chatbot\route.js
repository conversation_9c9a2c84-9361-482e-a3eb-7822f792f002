"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chatbot/route.ts */ \"(rsc)/./app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chatbot/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/chatbot/route.ts":
/*!**********************************!*\
  !*** ./app/api/chatbot/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY || \"\");\n// Product database for context\nconst PRODUCT_CONTEXT = `\nYou are an AI shopping assistant for an ecommerce platform. Here are our current products:\n\nELECTRONICS (12 products):\n- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation\n- Smart Fitness Watch ($199.99) - Advanced fitness tracking\n- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera\n- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features\n- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip\n- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling\n- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip\n- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen\n- LG 55\" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV\n- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency\n- Canon EOS R5 ($3899.99) - Professional mirrorless camera\n- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot\n\nFASHION (12 products):\n- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning\n- Classic Denim Jacket ($79.99) - Timeless denim jacket\n- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers\n- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost\n- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans\n- Zara Wool Coat ($199.99) - Elegant wool coat for winter\n- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors\n- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking\n- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket\n- Zara Leather Handbag ($149.99) - Premium leather with gold hardware\n- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket\n- H&M Summer Dress ($39.99) - Flowy summer dress in floral print\n\nSPORTS & FITNESS (12 products):\n- Professional Basketball ($34.99) - Official size basketball\n- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat\n- Nike Training Shoes ($119.99) - Cross-training shoes for gym\n- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball\n- Under Armour Gym Bag ($49.99) - Durable with multiple compartments\n- Puma Running Shorts ($24.99) - Lightweight with built-in brief\n- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts\n- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles\n- Adidas Tennis Racket ($149.99) - Professional with carbon frame\n- Under Armour Compression Shirt ($39.99) - Moisture-wicking\n- Puma Football Cleats ($89.99) - High-performance football cleats\n- Reebok Resistance Bands ($29.99) - Set for strength training\n\nHOME & GARDEN (12 products):\n- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb\n- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves\n- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms\n- Wayfair Dining Table ($599.99) - Solid wood dining table for 6\n- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows\n- West Elm Coffee Table ($399.99) - Mid-century modern coffee table\n- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors\n- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable\n- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle\n- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors\n- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade\n- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage\n\nSTORE POLICIES:\n- Free shipping on orders over $50\n- 30-day return policy\n- Standard shipping: 3-5 business days ($5.99)\n- Express shipping: 1-2 business days ($12.99)\n- International shipping available\n- We accept all major credit cards, PayPal, Apple Pay, Google Pay\n- Customer service: +1 (555) 123-4567\n- Email: <EMAIL>\n`;\nasync function POST(req) {\n    try {\n        const { message, sessionId } = await req.json();\n        if (!message || !sessionId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Message and sessionId are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if Gemini API key is available\n        if (!process.env.GEMINI_API_KEY) {\n            console.warn(\"GEMINI_API_KEY not found, using intelligent fallback responses\");\n            return getIntelligentFallbackResponse(message, sessionId);\n        }\n        try {\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-pro\"\n            });\n            const prompt = `${PRODUCT_CONTEXT}\n\nYou are a helpful, friendly AI shopping assistant. The customer says: \"${message}\"\n\nGuidelines:\n1. Be conversational and helpful\n2. Recommend specific products from our inventory with exact names and prices\n3. Ask follow-up questions to better understand their needs\n4. Provide helpful information about shipping, returns, and policies when relevant\n5. If they ask about products we don't have, suggest similar alternatives from our catalog\n6. Keep responses concise but informative (2-4 sentences max)\n7. Use emojis sparingly for a friendly tone\n8. Always try to guide them toward making a purchase or finding what they need\n9. Format product recommendations clearly with bullet points when listing multiple items\n\nRespond as a knowledgeable shopping assistant:`;\n            const result = await model.generateContent(prompt);\n            const response = result.response.text();\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                response,\n                sessionId\n            });\n        } catch (aiError) {\n            console.error(\"Gemini AI error:\", aiError);\n            // Fallback to predefined responses if AI fails\n            return getIntelligentFallbackResponse(message, sessionId);\n        }\n    } catch (error) {\n        console.error(\"Chatbot API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Intelligent fallback function for when AI is not available\nfunction getIntelligentFallbackResponse(message, sessionId) {\n    const lowerMessage = message.toLowerCase();\n    let response = \"\";\n    // Price-based queries\n    if (lowerMessage.includes(\"under\") || lowerMessage.includes(\"below\") || lowerMessage.includes(\"less than\")) {\n        const priceMatch = lowerMessage.match(/\\$?(\\d+)/);\n        const budget = priceMatch ? parseInt(priceMatch[1]) : 100;\n        if (lowerMessage.includes(\"headphones\") || lowerMessage.includes(\"earbuds\")) {\n            if (budget >= 200) {\n                response = `Great! For headphones under $${budget}, I recommend:\n\n• **Wireless Bluetooth Headphones** - $99.99 ✨\n  Premium wireless with noise cancellation - perfect choice!\n\n• **Sony WH-1000XM5** - $349.99 (if budget allows)\n  Industry-leading noise canceling headphones\n\n• **AirPods Pro 2** - $249.99\n  Wireless earbuds with adaptive transparency\n\nThe Wireless Bluetooth Headphones are our most popular choice in your budget range!`;\n            } else {\n                response = `For headphones under $${budget}, I have a perfect match:\n\n• **Wireless Bluetooth Headphones** - $99.99 ✨\n  Premium wireless headphones with noise cancellation\n\nThis is our best-selling option and fits perfectly in your budget! Would you like to know more about the features?`;\n            }\n        } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\")) {\n            response = `Here are some great electronics under $${budget}:\n\n• **Smart LED Light Bulb** - $24.99\n  WiFi-enabled smart LED bulb with color changing\n\n• **Nike Swim Goggles** - $19.99\n  Anti-fog swimming goggles\n\n${budget >= 100 ? \"• **Wireless Bluetooth Headphones** - $99.99\\n  Premium wireless headphones with noise cancellation\" : \"\"}\n\nWhat type of electronics are you most interested in?`;\n        } else {\n            response = `I can help you find great products under $${budget}! Here are some popular options:\n\n• **H&M Cotton T-Shirt** - $12.99 (Fashion)\n• **Smart LED Light Bulb** - $24.99 (Home)\n• **Adidas Soccer Ball** - $29.99 (Sports)\n${budget >= 50 ? \"• **Under Armour Gym Bag** - $49.99 (Sports)\" : \"\"}\n${budget >= 90 ? \"• **Blue Running Sneakers** - $89.99 (Fashion)\" : \"\"}\n\nWhat category interests you most?`;\n        }\n    } else if (lowerMessage.includes(\"sneakers\") || lowerMessage.includes(\"shoes\")) {\n        response = `I found some great sneakers for you! Here are my top recommendations:\n\n• **Blue Running Sneakers** - $89.99 ⭐\n  Comfortable blue running sneakers with advanced cushioning\n\n• **Nike Air Max 270** - $129.99\n  Iconic lifestyle sneakers with Air Max cushioning\n\n• **Adidas Ultraboost 22** - $179.99\n  Premium running shoes with Boost technology\n\nThe Blue Running Sneakers are very popular! Would you like to see more details?`;\n    } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\") || lowerMessage.includes(\"gadgets\")) {\n        response = `Here are some popular electronics from our collection:\n\n🎧 **Audio & Entertainment**\n• **Wireless Bluetooth Headphones** - $99.99\n• **Sony WH-1000XM5** - $349.99\n• **AirPods Pro 2** - $249.99\n\n📱 **Mobile & Computing**\n• **iPhone 15 Pro** - $999.99\n• **Samsung Galaxy S24** - $899.99\n• **MacBook Air M3** - $1299.99\n\n🏠 **Smart Home**\n• **Smart LED Light Bulb** - $24.99\n• **Philips Smart Thermostat** - $199.99\n\nWhat type of electronics are you most interested in?`;\n    } else if (lowerMessage.includes(\"fashion\") || lowerMessage.includes(\"clothing\") || lowerMessage.includes(\"clothes\")) {\n        response = `Here's our fashion collection:\n\n👟 **Footwear**\n• **Blue Running Sneakers** - $89.99\n• **Nike Air Max 270** - $129.99\n• **Adidas Ultraboost 22** - $179.99\n\n👕 **Clothing**\n• **H&M Cotton T-Shirt** - $12.99\n• **Classic Denim Jacket** - $79.99\n• **Zara Wool Coat** - $199.99\n\n👜 **Accessories**\n• **Zara Leather Handbag** - $149.99\n\nWhat style are you looking for?`;\n    } else if (lowerMessage.includes(\"sports\") || lowerMessage.includes(\"fitness\") || lowerMessage.includes(\"gym\")) {\n        response = `Great choice! Here's our sports & fitness collection:\n\n🏀 **Sports Equipment**\n• **Professional Basketball** - $34.99\n• **Adidas Soccer Ball** - $29.99\n• **Adidas Tennis Racket** - $149.99\n\n💪 **Fitness Gear**\n• **Yoga Mat Premium** - $39.99\n• **Reebok Dumbbells Set** - $199.99\n• **Under Armour Gym Bag** - $49.99\n\n👟 **Athletic Wear**\n• **Nike Training Shoes** - $119.99\n• **Under Armour Compression Shirt** - $39.99\n\nWhat sport or activity are you training for?`;\n    } else if (lowerMessage.includes(\"home\") || lowerMessage.includes(\"furniture\") || lowerMessage.includes(\"garden\")) {\n        response = `Here are some great home & garden items:\n\n💡 **Smart Home**\n• **Smart LED Light Bulb** - $24.99\n• **Philips Air Purifier** - $299.99\n• **Philips Smart Thermostat** - $199.99\n\n🪑 **Furniture**\n• **IKEA Bookshelf** - $79.99\n• **Wayfair Dining Table** - $599.99\n• **West Elm Coffee Table** - $399.99\n\n🌱 **Garden**\n• **Wayfair Garden Hose** - $39.99\n• **IKEA Plant Pot Set** - $29.99\n\nWhat room are you looking to improve?`;\n    } else if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"support\")) {\n        response = `I'm here to help! I can assist you with:\n\n🛍️ **Product Search**: Find specific products and get recommendations\n📱 **Categories**: Browse Electronics, Fashion, Sports, or Home & Garden\n🚚 **Shipping**: Free shipping on orders over $50\n💳 **Payment**: We accept all major payment methods\n📞 **Support**: Call us at +1 (555) 123-4567\n\nWhat would you like help with today?`;\n    } else if (lowerMessage.includes(\"blue\")) {\n        response = `Looking for blue items? Here are some great options:\n\n• **Blue Running Sneakers** - $89.99 ⭐\n  Comfortable blue running sneakers with advanced cushioning\n\nPerfect for running or casual wear! Would you like to see more blue products?`;\n    } else if (lowerMessage.includes(\"cheap\") || lowerMessage.includes(\"affordable\") || lowerMessage.includes(\"budget\")) {\n        response = `Here are some great budget-friendly options:\n\n💰 **Under $30**\n• **H&M Cotton T-Shirt** - $12.99\n• **Nike Swim Goggles** - $19.99\n• **Smart LED Light Bulb** - $24.99\n• **Adidas Soccer Ball** - $29.99\n\n💰 **Under $50**\n• **Professional Basketball** - $34.99\n• **Yoga Mat Premium** - $39.99\n• **Under Armour Gym Bag** - $49.99\n\nWhat's your budget range?`;\n    } else {\n        response = `Hello! I'm your AI shopping assistant 🤖\n\nI can help you find products from our collection:\n📱 **Electronics** - Phones, headphones, smart home\n👕 **Fashion** - Clothing, shoes, accessories\n🏃 **Sports** - Fitness gear, athletic wear\n🏠 **Home** - Furniture, decor, garden items\n\nTry asking me:\n• \"Show me wireless headphones under $200\"\n• \"I need blue sneakers\"\n• \"What electronics do you have?\"\n\nWhat are you looking for today?`;\n    }\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        response,\n        sessionId\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chatbot/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();