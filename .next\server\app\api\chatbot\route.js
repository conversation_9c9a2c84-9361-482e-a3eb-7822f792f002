"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chatbot/route";
exports.ids = ["app/api/chatbot/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chatbot/route.ts */ \"(rsc)/./app/api/chatbot/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chatbot/route\",\n        pathname: \"/api/chatbot\",\n        filename: \"route\",\n        bundlePath: \"app/api/chatbot/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\chatbot\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_chatbot_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chatbot/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/chatbot/route.ts":
/*!**********************************!*\
  !*** ./app/api/chatbot/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini AI\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(process.env.GEMINI_API_KEY || \"\");\n// Product database for context\nconst PRODUCT_CONTEXT = `\nYou are an AI shopping assistant for an ecommerce platform. Here are our current products:\n\nELECTRONICS (12 products):\n- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation\n- Smart Fitness Watch ($199.99) - Advanced fitness tracking\n- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera\n- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features\n- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip\n- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling\n- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip\n- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen\n- LG 55\" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV\n- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency\n- Canon EOS R5 ($3899.99) - Professional mirrorless camera\n- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot\n\nFASHION (12 products):\n- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning\n- Classic Denim Jacket ($79.99) - Timeless denim jacket\n- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers\n- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost\n- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans\n- Zara Wool Coat ($199.99) - Elegant wool coat for winter\n- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors\n- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking\n- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket\n- Zara Leather Handbag ($149.99) - Premium leather with gold hardware\n- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket\n- H&M Summer Dress ($39.99) - Flowy summer dress in floral print\n\nSPORTS & FITNESS (12 products):\n- Professional Basketball ($34.99) - Official size basketball\n- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat\n- Nike Training Shoes ($119.99) - Cross-training shoes for gym\n- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball\n- Under Armour Gym Bag ($49.99) - Durable with multiple compartments\n- Puma Running Shorts ($24.99) - Lightweight with built-in brief\n- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts\n- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles\n- Adidas Tennis Racket ($149.99) - Professional with carbon frame\n- Under Armour Compression Shirt ($39.99) - Moisture-wicking\n- Puma Football Cleats ($89.99) - High-performance football cleats\n- Reebok Resistance Bands ($29.99) - Set for strength training\n\nHOME & GARDEN (12 products):\n- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb\n- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves\n- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms\n- Wayfair Dining Table ($599.99) - Solid wood dining table for 6\n- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows\n- West Elm Coffee Table ($399.99) - Mid-century modern coffee table\n- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors\n- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable\n- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle\n- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors\n- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade\n- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage\n\nSTORE POLICIES:\n- Free shipping on orders over $50\n- 30-day return policy\n- Standard shipping: 3-5 business days ($5.99)\n- Express shipping: 1-2 business days ($12.99)\n- International shipping available\n- We accept all major credit cards, PayPal, Apple Pay, Google Pay\n- Customer service: +****************\n- Email: <EMAIL>\n`;\nasync function POST(req) {\n    try {\n        const { message, sessionId } = await req.json();\n        if (!message || !sessionId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Message and sessionId are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if Gemini API key is available\n        if (!process.env.GEMINI_API_KEY) {\n            console.warn(\"GEMINI_API_KEY not found, using intelligent fallback responses\");\n            return await getIntelligentFallbackResponse(message, sessionId);\n        }\n        try {\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-pro\"\n            });\n            const prompt = `${PRODUCT_CONTEXT}\n\nYou are a friendly, enthusiastic AI shopping assistant named Alex. You LOVE helping people find products and you're genuinely excited about shopping. The customer says: \"${message}\"\n\nPERSONALITY GUIDELINES:\n- Be conversational, warm, and enthusiastic (like talking to a friend who loves shopping)\n- Use natural language, contractions, and casual expressions\n- Show genuine interest in helping them find the perfect product\n- Use emojis naturally but not excessively (1-2 per response)\n- Ask follow-up questions to understand their needs better\n- Share personal opinions about products (\"I love this one!\" \"This is super popular!\")\n- Be encouraging and positive\n\nRESPONSE GUIDELINES:\n1. If they ask for a product we don't have, acknowledge it honestly and suggest great alternatives\n2. When recommending products, explain WHY they're good choices\n3. Ask about their specific needs (budget, use case, preferences)\n4. Keep responses conversational but informative (3-5 sentences)\n5. Always include specific product names and prices from our inventory\n6. Guide them naturally toward making a decision\n\nAVOID:\n- Robotic or formal language\n- Long lists without context\n- Generic responses\n- Being pushy or sales-y\n\nRespond as Alex, the enthusiastic shopping assistant who genuinely wants to help them find something perfect:`;\n            const result = await model.generateContent(prompt);\n            const response = result.response.text();\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                response,\n                sessionId\n            });\n        } catch (aiError) {\n            console.error(\"Gemini AI error:\", aiError);\n            // Fallback to predefined responses if AI fails\n            return await getIntelligentFallbackResponse(message, sessionId);\n        }\n    } catch (error) {\n        console.error(\"Chatbot API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Dynamic product search function\nasync function searchProducts(query, limit = 5) {\n    try {\n        const searchParams = new URLSearchParams({\n            search: query,\n            limit: limit.toString()\n        });\n        const response = await fetch(`${\"http://localhost:3000\" || 0}/api/products?${searchParams}`);\n        const data = await response.json();\n        return data.products || [];\n    } catch (error) {\n        console.error(\"Error searching products:\", error);\n        return [];\n    }\n}\n// Dynamic category search function\nasync function getProductsByCategory(category, limit = 5) {\n    try {\n        const searchParams = new URLSearchParams({\n            category: category,\n            limit: limit.toString()\n        });\n        const response = await fetch(`${\"http://localhost:3000\" || 0}/api/products?${searchParams}`);\n        const data = await response.json();\n        return data.products || [];\n    } catch (error) {\n        console.error(\"Error fetching category products:\", error);\n        return [];\n    }\n}\n// Dynamic price-based search function\nasync function getProductsByPriceRange(maxPrice, category, limit = 5) {\n    try {\n        const searchParams = new URLSearchParams({\n            maxPrice: maxPrice.toString(),\n            limit: limit.toString()\n        });\n        if (category) {\n            searchParams.append(\"category\", category);\n        }\n        const response = await fetch(`${\"http://localhost:3000\" || 0}/api/products?${searchParams}`);\n        const data = await response.json();\n        return data.products || [];\n    } catch (error) {\n        console.error(\"Error fetching products by price:\", error);\n        return [];\n    }\n}\n// Helper function to check if message contains product search intent\nasync function hasProductSearch(message) {\n    const productKeywords = [\n        \"looking for\",\n        \"need\",\n        \"want\",\n        \"show me\",\n        \"find\",\n        \"search\",\n        \"buy\",\n        \"hoodie\",\n        \"jacket\",\n        \"shirt\",\n        \"shoes\",\n        \"sneakers\",\n        \"headphones\",\n        \"phone\",\n        \"laptop\",\n        \"watch\",\n        \"bag\",\n        \"dress\",\n        \"jeans\",\n        \"coat\",\n        \"electronics\",\n        \"fashion\",\n        \"sports\",\n        \"home\",\n        \"furniture\",\n        \"tech\",\n        \"gadgets\"\n    ];\n    return productKeywords.some((keyword)=>message.toLowerCase().includes(keyword));\n}\n// Dynamic product search handler\nasync function handleProductSearch(message) {\n    const lowerMessage = message.toLowerCase();\n    // Extract price if mentioned\n    const priceMatch = lowerMessage.match(/under|below|less than.*?\\$?(\\d+)/);\n    const maxPrice = priceMatch ? parseInt(priceMatch[1]) : null;\n    // Extract category hints\n    let category = null;\n    if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\") || lowerMessage.includes(\"gadgets\") || lowerMessage.includes(\"phone\") || lowerMessage.includes(\"laptop\") || lowerMessage.includes(\"headphones\")) {\n        category = \"electronics\";\n    } else if (lowerMessage.includes(\"fashion\") || lowerMessage.includes(\"clothing\") || lowerMessage.includes(\"clothes\") || lowerMessage.includes(\"shirt\") || lowerMessage.includes(\"jacket\") || lowerMessage.includes(\"shoes\") || lowerMessage.includes(\"sneakers\") || lowerMessage.includes(\"dress\") || lowerMessage.includes(\"jeans\")) {\n        category = \"fashion\";\n    } else if (lowerMessage.includes(\"sports\") || lowerMessage.includes(\"fitness\") || lowerMessage.includes(\"gym\") || lowerMessage.includes(\"basketball\") || lowerMessage.includes(\"soccer\") || lowerMessage.includes(\"yoga\")) {\n        category = \"sports\";\n    } else if (lowerMessage.includes(\"home\") || lowerMessage.includes(\"furniture\") || lowerMessage.includes(\"garden\") || lowerMessage.includes(\"lamp\") || lowerMessage.includes(\"table\") || lowerMessage.includes(\"chair\")) {\n        category = \"home\";\n    }\n    let products = [];\n    // Search strategy: try specific search first, then category, then price range\n    if (maxPrice && category) {\n        products = await getProductsByPriceRange(maxPrice, category, 4);\n    } else if (maxPrice) {\n        products = await getProductsByPriceRange(maxPrice, undefined, 4);\n    } else if (category) {\n        products = await getProductsByCategory(category, 4);\n    } else {\n        // Extract key search terms\n        const searchTerms = lowerMessage.replace(/looking for|need|want|show me|find|search|buy/g, \"\").trim();\n        products = await searchProducts(searchTerms, 4);\n    }\n    // Generate dynamic response based on results\n    if (products.length === 0) {\n        return generateNoResultsResponse(lowerMessage, category);\n    } else {\n        return generateProductResponse(products, lowerMessage, maxPrice, category);\n    }\n}\n// Generate response when no products found\nfunction generateNoResultsResponse(query, category) {\n    const responses = [\n        `I'd love to help you find what you're looking for! 😊 I don't see exactly that in our current collection, but let me suggest some alternatives that might work for you. What specific features are most important to you?`,\n        `Hmm, I don't have exactly that right now, but I might have something similar! 🤔 Could you tell me a bit more about what you need it for? That'll help me find the perfect alternative!`,\n        `I want to help you find something great! While I don't have exactly that item, I have some similar options that might be even better. What's your budget range?`\n    ];\n    return responses[Math.floor(Math.random() * responses.length)];\n}\n// Generate response with product recommendations\nfunction generateProductResponse(products, query, maxPrice, category) {\n    const enthusiasm = [\n        \"Great choice!\",\n        \"Perfect!\",\n        \"Awesome!\",\n        \"I found some great options!\",\n        \"You're going to love these!\"\n    ];\n    const intro = enthusiasm[Math.floor(Math.random() * enthusiasm.length)];\n    let response = `${intro} 😊 Here's what I found for you:\\n\\n`;\n    products.forEach((product, index)=>{\n        const emoji = index === 0 ? \" ⭐\" : \"\";\n        response += `• **${product.name}** - $${product.price}${emoji}\\n`;\n        if (product.description) {\n            response += `  ${product.description}\\n\\n`;\n        }\n    });\n    // Add contextual follow-up based on search\n    if (maxPrice) {\n        response += `All of these fit within your $${maxPrice} budget! `;\n    }\n    if (products.length > 1) {\n        response += `Which one catches your eye? I can tell you more about any of them! 🤔`;\n    } else {\n        response += `This is really popular! Would you like to know more about the features? 😊`;\n    }\n    return response;\n}\n// Intelligent fallback function for when AI is not available\nasync function getIntelligentFallbackResponse(message, sessionId) {\n    const lowerMessage = message.toLowerCase();\n    let response = \"\";\n    // Greeting responses\n    if (lowerMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {\n        const greetings = [\n            \"Hi there! \\uD83D\\uDE0A I'm here to help you find exactly what you're looking for. What's on your shopping list today?\",\n            \"Hello! Great to see you! \\uD83D\\uDC4B I'd love to help you discover some amazing products. What can I help you find?\",\n            \"Hey! Welcome! I'm excited to help you shop. Are you looking for something specific today?\",\n            \"Hi! I'm your personal shopping assistant. What brings you here today - anything particular you need?\"\n        ];\n        response = greetings[Math.floor(Math.random() * greetings.length)];\n    } else if (await hasProductSearch(lowerMessage)) {\n        const searchResults = await handleProductSearch(lowerMessage);\n        response = searchResults;\n    } else if (lowerMessage.includes(\"electronics\") || lowerMessage.includes(\"tech\") || lowerMessage.includes(\"gadgets\")) {\n        const products = await getProductsByCategory(\"electronics\", 5);\n        if (products.length > 0) {\n            response = `Ooh, a tech lover! 🤓 Here are some cool electronics I think you'll love:\\n\\n`;\n            products.forEach((product, index)=>{\n                const emoji = index === 0 ? \" ⭐\" : \"\";\n                response += `• **${product.name}** - $${product.price}${emoji}\\n`;\n                if (product.description) response += `  ${product.description}\\n\\n`;\n            });\n            response += `What kind of tech are you in the mood for? Looking to upgrade something specific? 😊`;\n        } else {\n            response = `I'd love to show you our electronics! Let me check what's available right now... 🤔`;\n        }\n    } else if (lowerMessage.includes(\"fashion\") || lowerMessage.includes(\"clothing\") || lowerMessage.includes(\"clothes\")) {\n        const products = await getProductsByCategory(\"fashion\", 5);\n        if (products.length > 0) {\n            response = `Fashion time! 👗 Here's what's trending right now:\\n\\n`;\n            products.forEach((product, index)=>{\n                const emoji = index === 0 ? \" ⭐\" : \"\";\n                response += `• **${product.name}** - $${product.price}${emoji}\\n`;\n                if (product.description) response += `  ${product.description}\\n\\n`;\n            });\n            response += `What's your style vibe? Casual and comfy, or looking to dress up a bit? 😊`;\n        } else {\n            response = `I'd love to help you with fashion! Let me see what styles we have available... 👗`;\n        }\n    } else if (lowerMessage.includes(\"sports\") || lowerMessage.includes(\"fitness\") || lowerMessage.includes(\"gym\")) {\n        const products = await getProductsByCategory(\"sports\", 5);\n        if (products.length > 0) {\n            response = `Great choice! Here's our sports & fitness collection:\\n\\n`;\n            products.forEach((product, index)=>{\n                const emoji = index === 0 ? \" ⭐\" : \"\";\n                response += `• **${product.name}** - $${product.price}${emoji}\\n`;\n                if (product.description) response += `  ${product.description}\\n\\n`;\n            });\n            response += `What sport or activity are you training for? 💪`;\n        } else {\n            response = `I'd love to help you with sports gear! Let me check our current selection... 🏃‍♂️`;\n        }\n    } else if (lowerMessage.includes(\"home\") || lowerMessage.includes(\"furniture\") || lowerMessage.includes(\"garden\")) {\n        const products = await getProductsByCategory(\"home\", 5);\n        if (products.length > 0) {\n            response = `Perfect! Here are some great home & garden items:\\n\\n`;\n            products.forEach((product, index)=>{\n                const emoji = index === 0 ? \" ⭐\" : \"\";\n                response += `• **${product.name}** - $${product.price}${emoji}\\n`;\n                if (product.description) response += `  ${product.description}\\n\\n`;\n            });\n            response += `What room are you looking to improve? 🏠`;\n        } else {\n            response = `I'd love to help you with home items! Let me see what we have... 🏠`;\n        }\n    } else if (lowerMessage.includes(\"help\") || lowerMessage.includes(\"support\")) {\n        response = `Of course! I'm here to help with whatever you need! 😊\n\nI can help you with:\n🛍️ **Finding products** - Just tell me what you're looking for\n💰 **Budget questions** - I'll find great options in your price range\n🚚 **Shipping info** - We have free shipping on orders over $50!\n💳 **Payment stuff** - We accept pretty much everything\n📞 **Other questions** - Call our team at +****************\n\nWhat's on your mind? I'm here to make your shopping super easy! 🛒`;\n    } else {\n        // Handle unknown queries more naturally\n        const unknownResponses = [\n            `Hmm, I'm not sure I caught that exactly! 🤔 Could you tell me a bit more about what you're looking for? I've got electronics, fashion, sports gear, and home stuff!`,\n            `I want to help you find something great! Could you be a bit more specific? Are you shopping for yourself or maybe looking for a gift? 😊`,\n            `I'm here to help! What kind of product are you in the mood for today? Electronics, clothes, sports stuff, or maybe something for your home? 🛍️`,\n            `Let me help you find exactly what you need! What's on your shopping list today? I love helping people discover cool products! ✨`\n        ];\n        response = unknownResponses[Math.floor(Math.random() * unknownResponses.length)];\n    }\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        response,\n        sessionId\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chatbot/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchatbot%2Froute&page=%2Fapi%2Fchatbot%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchatbot%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();