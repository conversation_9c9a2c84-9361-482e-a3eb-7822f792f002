"use client"

import { useState } from "react"
import { use<PERSON><PERSON>er, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"

const categories = [
  { id: "electronics", name: "Electronics", count: 12 },
  { id: "fashion", name: "Fashion", count: 12 },
  { id: "home", name: "Home & Garden", count: 12 },
  { id: "sports", name: "Sports & Fitness", count: 12 },
]

// Dynamic brands based on category
const getBrandsForCategory = (category: string | null) => {
  const allBrands = {
    electronics: [
      { id: "apple", name: "<PERSON>", count: 3 },
      { id: "samsung", name: "<PERSON>", count: 2 },
      { id: "sony", name: "<PERSON>", count: 2 },
      { id: "bose", name: "<PERSON>", count: 1 },
      { id: "lg", name: "<PERSON><PERSON>", count: 1 },
    ],
    fashion: [
      { id: "nike", name: "Nike", count: 4 },
      { id: "adidas", name: "Adidas", count: 3 },
      { id: "levis", name: "Levi's", count: 2 },
      { id: "zara", name: "Zara", count: 2 },
      { id: "hm", name: "H&M", count: 1 },
    ],
    sports: [
      { id: "nike", name: "Nike", count: 3 },
      { id: "adidas", name: "Adidas", count: 2 },
      { id: "underarmour", name: "Under Armour", count: 2 },
      { id: "puma", name: "Puma", count: 1 },
      { id: "reebok", name: "Reebok", count: 1 },
    ],
    home: [
      { id: "ikea", name: "IKEA", count: 2 },
      { id: "philips", name: "Philips", count: 2 },
      { id: "wayfair", name: "Wayfair", count: 1 },
      { id: "pottery-barn", name: "Pottery Barn", count: 1 },
      { id: "west-elm", name: "West Elm", count: 1 },
    ],
  }

  if (!category) {
    // Return all brands if no category selected
    const allBrandsList = Object.values(allBrands).flat()
    const brandMap = new Map()
    allBrandsList.forEach(brand => {
      if (brandMap.has(brand.id)) {
        brandMap.get(brand.id).count += brand.count
      } else {
        brandMap.set(brand.id, { ...brand })
      }
    })
    return Array.from(brandMap.values()).slice(0, 8)
  }

  return allBrands[category as keyof typeof allBrands] || []
}

export function ProductFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [priceRange, setPriceRange] = useState([0, 1000])

  const currentCategory = searchParams.get("category")
  const brands = getBrandsForCategory(currentCategory)

  const updateFilters = (key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    // Reset to first page when filters change
    params.delete("page")
    
    router.push(`/products?${params.toString()}`)
  }

  const clearFilters = () => {
    router.push("/products")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Filters</h3>
        <Button variant="ghost" size="sm" onClick={clearFilters}>
          Clear All
        </Button>
      </div>

      {/* Price Range */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Price Range</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Slider
            value={priceRange}
            onValueChange={setPriceRange}
            max={1000}
            step={10}
            className="w-full"
          />
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              placeholder="Min"
              value={priceRange[0]}
              onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
              className="w-20"
            />
            <span>-</span>
            <Input
              type="number"
              placeholder="Max"
              value={priceRange[1]}
              onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000])}
              className="w-20"
            />
          </div>
          <Button
            size="sm"
            onClick={() => {
              updateFilters("minPrice", priceRange[0].toString())
              updateFilters("maxPrice", priceRange[1].toString())
            }}
            className="w-full"
          >
            Apply
          </Button>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Categories</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={category.id}
                checked={searchParams.get("category") === category.id}
                onCheckedChange={(checked) => {
                  updateFilters("category", checked ? category.id : null)
                }}
              />
              <Label
                htmlFor={category.id}
                className="text-sm font-normal cursor-pointer flex-1"
              >
                {category.name}
              </Label>
              <span className="text-xs text-muted-foreground">
                ({category.count})
              </span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Brands */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Brands</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center space-x-2">
              <Checkbox
                id={brand.id}
                checked={searchParams.get("brand") === brand.id}
                onCheckedChange={(checked) => {
                  updateFilters("brand", checked ? brand.id : null)
                }}
              />
              <Label
                htmlFor={brand.id}
                className="text-sm font-normal cursor-pointer flex-1"
              >
                {brand.name}
              </Label>
              <span className="text-xs text-muted-foreground">
                ({brand.count})
              </span>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Rating */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Customer Rating</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {[4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-2">
              <Checkbox
                id={`rating-${rating}`}
                checked={searchParams.get("minRating") === rating.toString()}
                onCheckedChange={(checked) => {
                  updateFilters("minRating", checked ? rating.toString() : null)
                }}
              />
              <Label
                htmlFor={`rating-${rating}`}
                className="text-sm font-normal cursor-pointer flex-1"
              >
                {rating}+ Stars
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
