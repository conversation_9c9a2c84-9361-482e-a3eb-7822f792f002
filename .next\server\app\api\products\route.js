"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZwcm9kdWN0cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGcHJvZHVjdHMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZwcm9kdWN0cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNPbWthciU1Q0RvY3VtZW50cyU1Q09ta2FyJTVDc3RhcnQlNUNwcm9qZWN0cyU1Q21vY2slNUNlY29tJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNPbWthciU1Q0RvY3VtZW50cyU1Q09ta2FyJTVDc3RhcnQlNUNwcm9qZWN0cyU1Q21vY2slNUNlY29tJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQzZDO0FBQzFIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDNko7O0FBRTdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWNvbW1lcmNlLWFwcC8/Y2I1ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxPbWthclxcXFxEb2N1bWVudHNcXFxcT21rYXJcXFxcc3RhcnRcXFxccHJvamVjdHNcXFxcbW9ja1xcXFxlY29tXFxcXGFwcFxcXFxhcGlcXFxccHJvZHVjdHNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Byb2R1Y3RzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvcHJvZHVjdHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3Byb2R1Y3RzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcT21rYXJcXFxcRG9jdW1lbnRzXFxcXE9ta2FyXFxcXHN0YXJ0XFxcXHByb2plY3RzXFxcXG1vY2tcXFxcZWNvbVxcXFxhcHBcXFxcYXBpXFxcXHByb2R1Y3RzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL3Byb2R1Y3RzL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCwgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./lib/db.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst productQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"1\"),\n    limit: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"12\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    search: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"name\",\n        \"price\",\n        \"createdAt\",\n        \"rating\"\n    ]).optional().default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).optional().default(\"desc\"),\n    minPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    maxPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const query = productQuerySchema.parse(Object.fromEntries(searchParams));\n        const page = parseInt(query.page);\n        const limit = parseInt(query.limit);\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {\n            isActive: true\n        };\n        if (query.category) {\n            where.category = {\n                slug: query.category\n            };\n        }\n        if (query.search) {\n            where.OR = [\n                {\n                    name: {\n                        contains: query.search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: query.search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    tags: {\n                        has: query.search\n                    }\n                }\n            ];\n        }\n        if (query.minPrice || query.maxPrice) {\n            where.price = {};\n            if (query.minPrice) {\n                where.price.gte = parseFloat(query.minPrice);\n            }\n            if (query.maxPrice) {\n                where.price.lte = parseFloat(query.maxPrice);\n            }\n        }\n        // Build orderBy clause\n        let orderBy = {};\n        if (query.sortBy === \"rating\") {\n            // For rating, we'll need to calculate average rating from reviews\n            orderBy = {\n                createdAt: query.sortOrder\n            };\n        } else {\n            orderBy[query.sortBy] = query.sortOrder;\n        }\n        // Get products\n        const [products, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.findMany({\n                where,\n                include: {\n                    category: {\n                        select: {\n                            id: true,\n                            name: true,\n                            slug: true\n                        }\n                    },\n                    images: {\n                        orderBy: {\n                            sortOrder: \"asc\"\n                        },\n                        take: 1\n                    },\n                    reviews: {\n                        select: {\n                            rating: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            reviews: true\n                        }\n                    }\n                },\n                orderBy,\n                skip,\n                take: limit\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.count({\n                where\n            })\n        ]);\n        // Calculate average ratings\n        const productsWithRatings = products.map((product)=>{\n            const avgRating = product.reviews.length > 0 ? product.reviews.reduce((sum, review)=>sum + review.rating, 0) / product.reviews.length : 0;\n            return {\n                id: product.id,\n                name: product.name,\n                slug: product.slug,\n                description: product.shortDescription || product.description,\n                price: product.price,\n                comparePrice: product.comparePrice,\n                image: product.images[0]?.url || null,\n                category: product.category,\n                rating: Math.round(avgRating * 10) / 10,\n                reviewCount: product._count.reviews,\n                isFeatured: product.isFeatured,\n                tags: product.tags\n            };\n        });\n        const totalPages = Math.ceil(total / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            products: productsWithRatings,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"Products API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        // This would be protected by admin middleware in a real app\n        const body = await req.json();\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.create({\n            data: {\n                ...body,\n                slug: body.slug || body.name.toLowerCase().replace(/\\s+/g, \"-\")\n            },\n            include: {\n                category: true,\n                images: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Create product error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/db.ts":
/*!*******************!*\
  !*** ./lib/db.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst db = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsS0FDWEYsZ0JBQWdCRyxNQUFNLElBQ3RCLElBQUlKLHdEQUFZQSxDQUFDO0lBQ2ZLLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUU7QUFFSixJQUFJQyxJQUF5QixFQUFjTCxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lY29tbWVyY2UtYXBwLy4vbGliL2RiLnRzPzFkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ3F1ZXJ5J10sXG4gIH0pXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gZGJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwiZGIiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();