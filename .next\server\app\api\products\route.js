"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./lib/db.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst productQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"1\"),\n    limit: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"12\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    search: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"name\",\n        \"price\",\n        \"createdAt\",\n        \"rating\"\n    ]).optional().default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).optional().default(\"desc\"),\n    minPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    maxPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const query = productQuerySchema.parse(Object.fromEntries(searchParams));\n        // Mock data for now since database might not be ready\n        const mockProducts = [\n            {\n                id: \"1\",\n                name: \"Wireless Bluetooth Headphones\",\n                slug: \"wireless-bluetooth-headphones\",\n                description: \"Premium wireless headphones with noise cancellation\",\n                price: 99.99,\n                comparePrice: 149.99,\n                image: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.5,\n                reviewCount: 128,\n                isFeatured: true,\n                tags: \"wireless,bluetooth,headphones,audio\"\n            },\n            {\n                id: \"2\",\n                name: \"Smart Fitness Watch\",\n                slug: \"smart-fitness-watch\",\n                description: \"Advanced fitness tracking smartwatch\",\n                price: 199.99,\n                comparePrice: 299.99,\n                image: \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"smartwatch,fitness,health,gps\"\n            },\n            {\n                id: \"3\",\n                name: \"Blue Running Sneakers\",\n                slug: \"blue-running-sneakers\",\n                description: \"Comfortable blue running sneakers\",\n                price: 89.99,\n                comparePrice: 120.00,\n                image: \"https://images.unsplash.com/photo-**********-7eec264c27ff?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.3,\n                reviewCount: 56,\n                isFeatured: true,\n                tags: \"sneakers,blue,running,shoes,sports\"\n            },\n            {\n                id: \"4\",\n                name: \"Classic Denim Jacket\",\n                slug: \"classic-denim-jacket\",\n                description: \"Timeless denim jacket\",\n                price: 79.99,\n                comparePrice: 110.00,\n                image: \"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.6,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"denim,jacket,classic,cotton\"\n            },\n            {\n                id: \"5\",\n                name: \"Professional Basketball\",\n                slug: \"professional-basketball\",\n                description: \"Official size basketball\",\n                price: 34.99,\n                comparePrice: 45.00,\n                image: \"https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.4,\n                reviewCount: 92,\n                isFeatured: false,\n                tags: \"basketball,sports,official,grip\"\n            },\n            {\n                id: \"6\",\n                name: \"Smart LED Light Bulb\",\n                slug: \"smart-led-light-bulb\",\n                description: \"WiFi-enabled smart LED bulb\",\n                price: 24.99,\n                comparePrice: 35.00,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.2,\n                reviewCount: 67,\n                isFeatured: false,\n                tags: \"smart,led,bulb,wifi,color\"\n            }\n        ];\n        let filteredProducts = [\n            ...mockProducts\n        ];\n        // Apply filters\n        if (query.category) {\n            filteredProducts = filteredProducts.filter((p)=>p.category.slug === query.category);\n        }\n        if (query.search) {\n            const searchLower = query.search.toLowerCase();\n            filteredProducts = filteredProducts.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower) || p.tags.toLowerCase().includes(searchLower));\n        }\n        if (query.minPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price >= parseFloat(query.minPrice));\n        }\n        if (query.maxPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price <= parseFloat(query.maxPrice));\n        }\n        // Apply sorting\n        if (query.sortBy === \"price\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.price - b.price : b.price - a.price);\n        } else if (query.sortBy === \"name\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name));\n        } else if (query.sortBy === \"rating\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.rating - b.rating : b.rating - a.rating);\n        }\n        // Apply pagination\n        const page = parseInt(query.page);\n        const limit = parseInt(query.limit);\n        const skip = (page - 1) * limit;\n        const total = filteredProducts.length;\n        const paginatedProducts = filteredProducts.slice(skip, skip + limit);\n        const totalPages = Math.ceil(total / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            products: paginatedProducts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"Products API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        // This would be protected by admin middleware in a real app\n        const body = await req.json();\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.create({\n            data: {\n                ...body,\n                slug: body.slug || body.name.toLowerCase().replace(/\\s+/g, \"-\")\n            },\n            include: {\n                category: true,\n                images: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Create product error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/db.ts":
/*!*******************!*\
  !*** ./lib/db.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst db = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsS0FDWEYsZ0JBQWdCRyxNQUFNLElBQ3RCLElBQUlKLHdEQUFZQSxDQUFDO0lBQ2ZLLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUU7QUFFSixJQUFJQyxJQUF5QixFQUFjTCxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lY29tbWVyY2UtYXBwLy4vbGliL2RiLnRzPzFkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ3F1ZXJ5J10sXG4gIH0pXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gZGJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwiZGIiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();