"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./lib/db.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst productQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"1\"),\n    limit: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"12\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    search: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"name\",\n        \"price\",\n        \"createdAt\",\n        \"rating\"\n    ]).optional().default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).optional().default(\"desc\"),\n    minPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    maxPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    brand: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const query = productQuerySchema.parse(Object.fromEntries(searchParams));\n        // Comprehensive mock data with 10+ products per category\n        const mockProducts = [\n            // ELECTRONICS (12 products)\n            {\n                id: \"1\",\n                name: \"Wireless Bluetooth Headphones\",\n                slug: \"wireless-bluetooth-headphones\",\n                description: \"Premium wireless headphones with noise cancellation\",\n                price: 99.99,\n                comparePrice: 149.99,\n                image: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.5,\n                reviewCount: 128,\n                isFeatured: true,\n                tags: \"wireless,bluetooth,headphones,audio,bose\"\n            },\n            {\n                id: \"2\",\n                name: \"Smart Fitness Watch\",\n                slug: \"smart-fitness-watch\",\n                description: \"Advanced fitness tracking smartwatch\",\n                price: 199.99,\n                comparePrice: 299.99,\n                image: \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"smartwatch,fitness,health,gps,apple\"\n            },\n            {\n                id: \"7\",\n                name: \"iPhone 15 Pro\",\n                slug: \"iphone-15-pro\",\n                description: \"Latest iPhone with advanced camera system\",\n                price: 999.99,\n                comparePrice: 1099.99,\n                image: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.9,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"smartphone,iphone,apple,camera,5g\"\n            },\n            {\n                id: \"8\",\n                name: \"Samsung Galaxy S24\",\n                slug: \"samsung-galaxy-s24\",\n                description: \"Flagship Android smartphone with AI features\",\n                price: 899.99,\n                comparePrice: 999.99,\n                image: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.7,\n                reviewCount: 324,\n                isFeatured: true,\n                tags: \"smartphone,samsung,android,camera,ai\"\n            },\n            {\n                id: \"9\",\n                name: \"MacBook Air M3\",\n                slug: \"macbook-air-m3\",\n                description: \"Ultra-thin laptop with M3 chip\",\n                price: 1299.99,\n                comparePrice: 1399.99,\n                image: \"https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 234,\n                isFeatured: true,\n                tags: \"laptop,macbook,apple,m3,ultrabook\"\n            },\n            {\n                id: \"10\",\n                name: \"Sony WH-1000XM5\",\n                slug: \"sony-wh-1000xm5\",\n                description: \"Industry-leading noise canceling headphones\",\n                price: 349.99,\n                comparePrice: 399.99,\n                image: \"https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.6,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"headphones,sony,noise-canceling,wireless\"\n            },\n            {\n                id: \"11\",\n                name: \"iPad Pro 12.9\",\n                slug: \"ipad-pro-12-9\",\n                description: \"Professional tablet with M2 chip\",\n                price: 1099.99,\n                comparePrice: 1199.99,\n                image: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.7,\n                reviewCount: 189,\n                isFeatured: false,\n                tags: \"tablet,ipad,apple,m2,professional\"\n            },\n            {\n                id: \"12\",\n                name: \"Nintendo Switch OLED\",\n                slug: \"nintendo-switch-oled\",\n                description: \"Gaming console with vibrant OLED screen\",\n                price: 349.99,\n                comparePrice: 379.99,\n                image: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.5,\n                reviewCount: 445,\n                isFeatured: false,\n                tags: \"gaming,nintendo,console,oled,portable\"\n            },\n            {\n                id: \"13\",\n                name: \"LG 55 4K OLED TV\",\n                slug: \"lg-55-4k-oled-tv\",\n                description: \"Premium 4K OLED smart TV\",\n                price: 1499.99,\n                comparePrice: 1799.99,\n                image: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.6,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"tv,oled,4k,smart,lg\"\n            },\n            {\n                id: \"14\",\n                name: \"AirPods Pro 2\",\n                slug: \"airpods-pro-2\",\n                description: \"Wireless earbuds with adaptive transparency\",\n                price: 249.99,\n                comparePrice: 279.99,\n                image: \"https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.4,\n                reviewCount: 678,\n                isFeatured: false,\n                tags: \"earbuds,airpods,apple,wireless,noise-canceling\"\n            },\n            {\n                id: \"15\",\n                name: \"Canon EOS R5\",\n                slug: \"canon-eos-r5\",\n                description: \"Professional mirrorless camera\",\n                price: 3899.99,\n                comparePrice: 4199.99,\n                image: \"https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 89,\n                isFeatured: false,\n                tags: \"camera,canon,mirrorless,professional,photography\"\n            },\n            {\n                id: \"16\",\n                name: \"Tesla Model S Plaid\",\n                slug: \"tesla-model-s-plaid\",\n                description: \"Electric vehicle with autopilot\",\n                price: 89999.99,\n                comparePrice: 94999.99,\n                image: \"https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.9,\n                reviewCount: 45,\n                isFeatured: true,\n                tags: \"electric,car,tesla,autopilot,luxury\"\n            },\n            // FASHION (12 products)\n            {\n                id: \"3\",\n                name: \"Blue Running Sneakers\",\n                slug: \"blue-running-sneakers\",\n                description: \"Comfortable blue running sneakers\",\n                price: 89.99,\n                comparePrice: 120.00,\n                image: \"https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.3,\n                reviewCount: 56,\n                isFeatured: true,\n                tags: \"sneakers,blue,running,shoes,sports,nike\"\n            },\n            {\n                id: \"4\",\n                name: \"Classic Denim Jacket\",\n                slug: \"classic-denim-jacket\",\n                description: \"Timeless denim jacket\",\n                price: 79.99,\n                comparePrice: 110.00,\n                image: \"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.6,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"denim,jacket,classic,cotton,levis\"\n            },\n            {\n                id: \"17\",\n                name: \"Nike Air Max 270\",\n                slug: \"nike-air-max-270\",\n                description: \"Iconic lifestyle sneakers with Air Max cushioning\",\n                price: 129.99,\n                comparePrice: 150.00,\n                image: \"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.5,\n                reviewCount: 789,\n                isFeatured: true,\n                tags: \"sneakers,nike,air-max,lifestyle,cushioning\"\n            },\n            {\n                id: \"18\",\n                name: \"Adidas Ultraboost 22\",\n                slug: \"adidas-ultraboost-22\",\n                description: \"Premium running shoes with Boost technology\",\n                price: 179.99,\n                comparePrice: 200.00,\n                image: \"https://images.unsplash.com/photo-1608231387042-66d1773070a5?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.7,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"sneakers,adidas,ultraboost,running,boost\"\n            },\n            {\n                id: \"19\",\n                name: \"Levi's 501 Original Jeans\",\n                slug: \"levis-501-original-jeans\",\n                description: \"Classic straight-leg jeans\",\n                price: 69.99,\n                comparePrice: 89.99,\n                image: \"https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.4,\n                reviewCount: 1234,\n                isFeatured: false,\n                tags: \"jeans,levis,501,classic,denim\"\n            },\n            {\n                id: \"20\",\n                name: \"Zara Wool Coat\",\n                slug: \"zara-wool-coat\",\n                description: \"Elegant wool coat for winter\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1539533018447-63fcce2678e3?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.2,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"coat,wool,winter,elegant,zara\"\n            },\n            {\n                id: \"21\",\n                name: \"H&M Cotton T-Shirt\",\n                slug: \"hm-cotton-t-shirt\",\n                description: \"Basic cotton t-shirt in multiple colors\",\n                price: 12.99,\n                comparePrice: 19.99,\n                image: \"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.0,\n                reviewCount: 2345,\n                isFeatured: false,\n                tags: \"t-shirt,cotton,basic,casual,hm\"\n            },\n            {\n                id: \"22\",\n                name: \"Nike Dri-FIT Shorts\",\n                slug: \"nike-dri-fit-shorts\",\n                description: \"Athletic shorts with moisture-wicking technology\",\n                price: 34.99,\n                comparePrice: 45.00,\n                image: \"https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.3,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"shorts,athletic,nike,dri-fit,sports\"\n            },\n            {\n                id: \"23\",\n                name: \"Adidas Track Jacket\",\n                slug: \"adidas-track-jacket\",\n                description: \"Classic three-stripe track jacket\",\n                price: 79.99,\n                comparePrice: 99.99,\n                image: \"https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.5,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"jacket,track,adidas,three-stripe,athletic\"\n            },\n            {\n                id: \"24\",\n                name: \"Zara Leather Handbag\",\n                slug: \"zara-leather-handbag\",\n                description: \"Premium leather handbag with gold hardware\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.6,\n                reviewCount: 123,\n                isFeatured: true,\n                tags: \"handbag,leather,premium,zara,accessories\"\n            },\n            {\n                id: \"25\",\n                name: \"Levi's Trucker Jacket\",\n                slug: \"levis-trucker-jacket\",\n                description: \"Iconic denim trucker jacket\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.7,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"jacket,denim,trucker,levis,classic\"\n            },\n            {\n                id: \"26\",\n                name: \"H&M Summer Dress\",\n                slug: \"hm-summer-dress\",\n                description: \"Flowy summer dress in floral print\",\n                price: 39.99,\n                comparePrice: 59.99,\n                image: \"https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.1,\n                reviewCount: 456,\n                isFeatured: false,\n                tags: \"dress,summer,floral,casual,hm\"\n            },\n            // SPORTS & FITNESS (12 products)\n            {\n                id: \"5\",\n                name: \"Professional Basketball\",\n                slug: \"professional-basketball\",\n                description: \"Official size basketball\",\n                price: 34.99,\n                comparePrice: 45.00,\n                image: \"https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.4,\n                reviewCount: 92,\n                isFeatured: false,\n                tags: \"basketball,sports,official,grip,nike\"\n            },\n            {\n                id: \"27\",\n                name: \"Yoga Mat Premium\",\n                slug: \"yoga-mat-premium\",\n                description: \"Non-slip premium yoga mat\",\n                price: 39.99,\n                comparePrice: 55.00,\n                image: \"https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.2,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"yoga,mat,premium,non-slip,fitness\"\n            },\n            {\n                id: \"28\",\n                name: \"Nike Training Shoes\",\n                slug: \"nike-training-shoes\",\n                description: \"Cross-training shoes for gym workouts\",\n                price: 119.99,\n                comparePrice: 140.00,\n                image: \"https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.6,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"training,shoes,nike,gym,cross-training\"\n            },\n            {\n                id: \"29\",\n                name: \"Adidas Soccer Ball\",\n                slug: \"adidas-soccer-ball\",\n                description: \"FIFA-approved soccer ball\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1614632537190-23e4b2e69c88?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.5,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"soccer,ball,adidas,fifa,football\"\n            },\n            {\n                id: \"30\",\n                name: \"Under Armour Gym Bag\",\n                slug: \"under-armour-gym-bag\",\n                description: \"Durable gym bag with multiple compartments\",\n                price: 49.99,\n                comparePrice: 69.99,\n                image: \"https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.3,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"gym,bag,under-armour,durable,sports\"\n            },\n            {\n                id: \"31\",\n                name: \"Puma Running Shorts\",\n                slug: \"puma-running-shorts\",\n                description: \"Lightweight running shorts with built-in brief\",\n                price: 24.99,\n                comparePrice: 34.99,\n                image: \"https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.1,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"running,shorts,puma,lightweight,athletic\"\n            },\n            {\n                id: \"32\",\n                name: \"Reebok Dumbbells Set\",\n                slug: \"reebok-dumbbells-set\",\n                description: \"Adjustable dumbbells for home workouts\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.7,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"dumbbells,weights,reebok,adjustable,home-gym\"\n            },\n            {\n                id: \"33\",\n                name: \"Nike Swim Goggles\",\n                slug: \"nike-swim-goggles\",\n                description: \"Anti-fog swimming goggles\",\n                price: 19.99,\n                comparePrice: 29.99,\n                image: \"https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.0,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"swimming,goggles,nike,anti-fog,water-sports\"\n            },\n            {\n                id: \"34\",\n                name: \"Adidas Tennis Racket\",\n                slug: \"adidas-tennis-racket\",\n                description: \"Professional tennis racket with carbon frame\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.4,\n                reviewCount: 78,\n                isFeatured: false,\n                tags: \"tennis,racket,adidas,professional,carbon\"\n            },\n            {\n                id: \"35\",\n                name: \"Under Armour Compression Shirt\",\n                slug: \"under-armour-compression-shirt\",\n                description: \"Moisture-wicking compression shirt\",\n                price: 39.99,\n                comparePrice: 54.99,\n                image: \"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.2,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"compression,shirt,under-armour,moisture-wicking,athletic\"\n            },\n            {\n                id: \"36\",\n                name: \"Puma Football Cleats\",\n                slug: \"puma-football-cleats\",\n                description: \"High-performance football cleats\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.5,\n                reviewCount: 156,\n                isFeatured: false,\n                tags: \"football,cleats,puma,performance,sports\"\n            },\n            {\n                id: \"37\",\n                name: \"Reebok Resistance Bands\",\n                slug: \"reebok-resistance-bands\",\n                description: \"Set of resistance bands for strength training\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.3,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"resistance,bands,reebok,strength,training\"\n            },\n            // HOME & GARDEN (12 products)\n            {\n                id: \"6\",\n                name: \"Smart LED Light Bulb\",\n                slug: \"smart-led-light-bulb\",\n                description: \"WiFi-enabled smart LED bulb\",\n                price: 24.99,\n                comparePrice: 35.00,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.2,\n                reviewCount: 67,\n                isFeatured: false,\n                tags: \"smart,led,bulb,wifi,color,philips\"\n            },\n            {\n                id: \"38\",\n                name: \"IKEA Bookshelf\",\n                slug: \"ikea-bookshelf\",\n                description: \"Modern wooden bookshelf with 5 shelves\",\n                price: 79.99,\n                comparePrice: 99.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.4,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"bookshelf,furniture,ikea,wooden,storage\"\n            },\n            {\n                id: \"39\",\n                name: \"Philips Air Purifier\",\n                slug: \"philips-air-purifier\",\n                description: \"HEPA air purifier for large rooms\",\n                price: 299.99,\n                comparePrice: 349.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.6,\n                reviewCount: 156,\n                isFeatured: true,\n                tags: \"air-purifier,philips,hepa,clean-air,home\"\n            },\n            {\n                id: \"40\",\n                name: \"Wayfair Dining Table\",\n                slug: \"wayfair-dining-table\",\n                description: \"Solid wood dining table for 6 people\",\n                price: 599.99,\n                comparePrice: 749.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.5,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"dining-table,furniture,wayfair,wood,family\"\n            },\n            {\n                id: \"41\",\n                name: \"Pottery Barn Throw Pillows\",\n                slug: \"pottery-barn-throw-pillows\",\n                description: \"Set of 4 decorative throw pillows\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.3,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"pillows,decorative,pottery-barn,home-decor,comfort\"\n            },\n            {\n                id: \"42\",\n                name: \"West Elm Coffee Table\",\n                slug: \"west-elm-coffee-table\",\n                description: \"Mid-century modern coffee table\",\n                price: 399.99,\n                comparePrice: 499.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.4,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"coffee-table,furniture,west-elm,mid-century,modern\"\n            },\n            {\n                id: \"43\",\n                name: \"IKEA Kitchen Cabinet\",\n                slug: \"ikea-kitchen-cabinet\",\n                description: \"Modular kitchen cabinet with soft-close doors\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.2,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"kitchen,cabinet,ikea,modular,storage\"\n            },\n            {\n                id: \"44\",\n                name: \"Philips Smart Thermostat\",\n                slug: \"philips-smart-thermostat\",\n                description: \"WiFi-enabled programmable thermostat\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.5,\n                reviewCount: 234,\n                isFeatured: true,\n                tags: \"thermostat,smart,philips,wifi,energy-saving\"\n            },\n            {\n                id: \"45\",\n                name: \"Wayfair Garden Hose\",\n                slug: \"wayfair-garden-hose\",\n                description: \"50ft expandable garden hose with spray nozzle\",\n                price: 39.99,\n                comparePrice: 59.99,\n                image: \"https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.1,\n                reviewCount: 456,\n                isFeatured: false,\n                tags: \"garden,hose,wayfair,expandable,outdoor\"\n            },\n            {\n                id: \"46\",\n                name: \"Pottery Barn Area Rug\",\n                slug: \"pottery-barn-area-rug\",\n                description: \"Hand-woven area rug in neutral colors\",\n                price: 299.99,\n                comparePrice: 399.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.6,\n                reviewCount: 78,\n                isFeatured: false,\n                tags: \"rug,area-rug,pottery-barn,hand-woven,home-decor\"\n            },\n            {\n                id: \"47\",\n                name: \"West Elm Floor Lamp\",\n                slug: \"west-elm-floor-lamp\",\n                description: \"Modern brass floor lamp with fabric shade\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.3,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"lamp,floor-lamp,west-elm,brass,lighting\"\n            },\n            {\n                id: \"48\",\n                name: \"IKEA Plant Pot Set\",\n                slug: \"ikea-plant-pot-set\",\n                description: \"Set of 3 ceramic plant pots with drainage\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.0,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"plant-pot,ceramic,ikea,drainage,gardening\"\n            }\n        ];\n        let filteredProducts = [\n            ...mockProducts\n        ];\n        // Apply filters\n        if (query.category) {\n            filteredProducts = filteredProducts.filter((p)=>p.category.slug === query.category);\n        }\n        if (query.search) {\n            const searchLower = query.search.toLowerCase();\n            filteredProducts = filteredProducts.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower) || p.tags.toLowerCase().includes(searchLower));\n        }\n        if (query.minPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price >= parseFloat(query.minPrice));\n        }\n        if (query.maxPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price <= parseFloat(query.maxPrice));\n        }\n        // Apply brand filter\n        if (query.brand) {\n            filteredProducts = filteredProducts.filter((p)=>p.tags.toLowerCase().includes(query.brand.toLowerCase()));\n        }\n        // Apply sorting\n        if (query.sortBy === \"price\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.price - b.price : b.price - a.price);\n        } else if (query.sortBy === \"name\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name));\n        } else if (query.sortBy === \"rating\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.rating - b.rating : b.rating - a.rating);\n        }\n        // Apply pagination\n        const page = parseInt(query.page);\n        const limit = parseInt(query.limit);\n        const skip = (page - 1) * limit;\n        const total = filteredProducts.length;\n        const paginatedProducts = filteredProducts.slice(skip, skip + limit);\n        const totalPages = Math.ceil(total / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            products: paginatedProducts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"Products API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        // This would be protected by admin middleware in a real app\n        const body = await req.json();\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.create({\n            data: {\n                ...body,\n                slug: body.slug || body.name.toLowerCase().replace(/\\s+/g, \"-\")\n            },\n            include: {\n                category: true,\n                images: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Create product error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/db.ts":
/*!*******************!*\
  !*** ./lib/db.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst db = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsS0FDWEYsZ0JBQWdCRyxNQUFNLElBQ3RCLElBQUlKLHdEQUFZQSxDQUFDO0lBQ2ZLLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUU7QUFFSixJQUFJQyxJQUF5QixFQUFjTCxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lY29tbWVyY2UtYXBwLy4vbGliL2RiLnRzPzFkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ3F1ZXJ5J10sXG4gIH0pXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gZGJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwiZGIiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();