"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/products/route.ts */ \"(rsc)/./app/api/products/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Omkar_Documents_Omkar_start_projects_mock_ecom_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/products/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/products/route.ts":
/*!***********************************!*\
  !*** ./app/api/products/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./lib/db.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\nconst productQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    page: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"1\"),\n    limit: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional().default(\"12\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    search: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"name\",\n        \"price\",\n        \"createdAt\",\n        \"rating\"\n    ]).optional().default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).optional().default(\"desc\"),\n    minPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    maxPrice: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n});\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const query = productQuerySchema.parse(Object.fromEntries(searchParams));\n        // Comprehensive mock data with 10+ products per category\n        const mockProducts = [\n            // ELECTRONICS (12 products)\n            {\n                id: \"1\",\n                name: \"Wireless Bluetooth Headphones\",\n                slug: \"wireless-bluetooth-headphones\",\n                description: \"Premium wireless headphones with noise cancellation\",\n                price: 99.99,\n                comparePrice: 149.99,\n                image: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.5,\n                reviewCount: 128,\n                isFeatured: true,\n                tags: \"wireless,bluetooth,headphones,audio,bose\"\n            },\n            {\n                id: \"2\",\n                name: \"Smart Fitness Watch\",\n                slug: \"smart-fitness-watch\",\n                description: \"Advanced fitness tracking smartwatch\",\n                price: 199.99,\n                comparePrice: 299.99,\n                image: \"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"smartwatch,fitness,health,gps,apple\"\n            },\n            {\n                id: \"7\",\n                name: \"iPhone 15 Pro\",\n                slug: \"iphone-15-pro\",\n                description: \"Latest iPhone with advanced camera system\",\n                price: 999.99,\n                comparePrice: 1099.99,\n                image: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.9,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"smartphone,iphone,apple,camera,5g\"\n            },\n            {\n                id: \"8\",\n                name: \"Samsung Galaxy S24\",\n                slug: \"samsung-galaxy-s24\",\n                description: \"Flagship Android smartphone with AI features\",\n                price: 899.99,\n                comparePrice: 999.99,\n                image: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.7,\n                reviewCount: 324,\n                isFeatured: true,\n                tags: \"smartphone,samsung,android,camera,ai\"\n            },\n            {\n                id: \"9\",\n                name: \"MacBook Air M3\",\n                slug: \"macbook-air-m3\",\n                description: \"Ultra-thin laptop with M3 chip\",\n                price: 1299.99,\n                comparePrice: 1399.99,\n                image: \"https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 234,\n                isFeatured: true,\n                tags: \"laptop,macbook,apple,m3,ultrabook\"\n            },\n            {\n                id: \"10\",\n                name: \"Sony WH-1000XM5\",\n                slug: \"sony-wh-1000xm5\",\n                description: \"Industry-leading noise canceling headphones\",\n                price: 349.99,\n                comparePrice: 399.99,\n                image: \"https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.6,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"headphones,sony,noise-canceling,wireless\"\n            },\n            {\n                id: \"11\",\n                name: \"iPad Pro 12.9\",\n                slug: \"ipad-pro-12-9\",\n                description: \"Professional tablet with M2 chip\",\n                price: 1099.99,\n                comparePrice: 1199.99,\n                image: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.7,\n                reviewCount: 189,\n                isFeatured: false,\n                tags: \"tablet,ipad,apple,m2,professional\"\n            },\n            {\n                id: \"12\",\n                name: \"Nintendo Switch OLED\",\n                slug: \"nintendo-switch-oled\",\n                description: \"Gaming console with vibrant OLED screen\",\n                price: 349.99,\n                comparePrice: 379.99,\n                image: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.5,\n                reviewCount: 445,\n                isFeatured: false,\n                tags: \"gaming,nintendo,console,oled,portable\"\n            },\n            {\n                id: \"13\",\n                name: \"LG 55 4K OLED TV\",\n                slug: \"lg-55-4k-oled-tv\",\n                description: \"Premium 4K OLED smart TV\",\n                price: 1499.99,\n                comparePrice: 1799.99,\n                image: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.6,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"tv,oled,4k,smart,lg\"\n            },\n            {\n                id: \"14\",\n                name: \"AirPods Pro 2\",\n                slug: \"airpods-pro-2\",\n                description: \"Wireless earbuds with adaptive transparency\",\n                price: 249.99,\n                comparePrice: 279.99,\n                image: \"https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.4,\n                reviewCount: 678,\n                isFeatured: false,\n                tags: \"earbuds,airpods,apple,wireless,noise-canceling\"\n            },\n            {\n                id: \"15\",\n                name: \"Canon EOS R5\",\n                slug: \"canon-eos-r5\",\n                description: \"Professional mirrorless camera\",\n                price: 3899.99,\n                comparePrice: 4199.99,\n                image: \"https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.8,\n                reviewCount: 89,\n                isFeatured: false,\n                tags: \"camera,canon,mirrorless,professional,photography\"\n            },\n            {\n                id: \"16\",\n                name: \"Tesla Model S Plaid\",\n                slug: \"tesla-model-s-plaid\",\n                description: \"Electric vehicle with autopilot\",\n                price: 89999.99,\n                comparePrice: 94999.99,\n                image: \"https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"electronics\",\n                    name: \"Electronics\",\n                    slug: \"electronics\"\n                },\n                rating: 4.9,\n                reviewCount: 45,\n                isFeatured: true,\n                tags: \"electric,car,tesla,autopilot,luxury\"\n            },\n            // FASHION (12 products)\n            {\n                id: \"3\",\n                name: \"Blue Running Sneakers\",\n                slug: \"blue-running-sneakers\",\n                description: \"Comfortable blue running sneakers\",\n                price: 89.99,\n                comparePrice: 120.00,\n                image: \"https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.3,\n                reviewCount: 56,\n                isFeatured: true,\n                tags: \"sneakers,blue,running,shoes,sports,nike\"\n            },\n            {\n                id: \"4\",\n                name: \"Classic Denim Jacket\",\n                slug: \"classic-denim-jacket\",\n                description: \"Timeless denim jacket\",\n                price: 79.99,\n                comparePrice: 110.00,\n                image: \"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.6,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"denim,jacket,classic,cotton,levis\"\n            },\n            {\n                id: \"17\",\n                name: \"Nike Air Max 270\",\n                slug: \"nike-air-max-270\",\n                description: \"Iconic lifestyle sneakers with Air Max cushioning\",\n                price: 129.99,\n                comparePrice: 150.00,\n                image: \"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.5,\n                reviewCount: 789,\n                isFeatured: true,\n                tags: \"sneakers,nike,air-max,lifestyle,cushioning\"\n            },\n            {\n                id: \"18\",\n                name: \"Adidas Ultraboost 22\",\n                slug: \"adidas-ultraboost-22\",\n                description: \"Premium running shoes with Boost technology\",\n                price: 179.99,\n                comparePrice: 200.00,\n                image: \"https://images.unsplash.com/photo-1608231387042-66d1773070a5?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.7,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"sneakers,adidas,ultraboost,running,boost\"\n            },\n            {\n                id: \"19\",\n                name: \"Levi's 501 Original Jeans\",\n                slug: \"levis-501-original-jeans\",\n                description: \"Classic straight-leg jeans\",\n                price: 69.99,\n                comparePrice: 89.99,\n                image: \"https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.4,\n                reviewCount: 1234,\n                isFeatured: false,\n                tags: \"jeans,levis,501,classic,denim\"\n            },\n            {\n                id: \"20\",\n                name: \"Zara Wool Coat\",\n                slug: \"zara-wool-coat\",\n                description: \"Elegant wool coat for winter\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1539533018447-63fcce2678e3?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.2,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"coat,wool,winter,elegant,zara\"\n            },\n            {\n                id: \"21\",\n                name: \"H&M Cotton T-Shirt\",\n                slug: \"hm-cotton-t-shirt\",\n                description: \"Basic cotton t-shirt in multiple colors\",\n                price: 12.99,\n                comparePrice: 19.99,\n                image: \"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.0,\n                reviewCount: 2345,\n                isFeatured: false,\n                tags: \"t-shirt,cotton,basic,casual,hm\"\n            },\n            {\n                id: \"22\",\n                name: \"Nike Dri-FIT Shorts\",\n                slug: \"nike-dri-fit-shorts\",\n                description: \"Athletic shorts with moisture-wicking technology\",\n                price: 34.99,\n                comparePrice: 45.00,\n                image: \"https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.3,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"shorts,athletic,nike,dri-fit,sports\"\n            },\n            {\n                id: \"23\",\n                name: \"Adidas Track Jacket\",\n                slug: \"adidas-track-jacket\",\n                description: \"Classic three-stripe track jacket\",\n                price: 79.99,\n                comparePrice: 99.99,\n                image: \"https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.5,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"jacket,track,adidas,three-stripe,athletic\"\n            },\n            {\n                id: \"24\",\n                name: \"Zara Leather Handbag\",\n                slug: \"zara-leather-handbag\",\n                description: \"Premium leather handbag with gold hardware\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.6,\n                reviewCount: 123,\n                isFeatured: true,\n                tags: \"handbag,leather,premium,zara,accessories\"\n            },\n            {\n                id: \"25\",\n                name: \"Levi's Trucker Jacket\",\n                slug: \"levis-trucker-jacket\",\n                description: \"Iconic denim trucker jacket\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.7,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"jacket,denim,trucker,levis,classic\"\n            },\n            {\n                id: \"26\",\n                name: \"H&M Summer Dress\",\n                slug: \"hm-summer-dress\",\n                description: \"Flowy summer dress in floral print\",\n                price: 39.99,\n                comparePrice: 59.99,\n                image: \"https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"fashion\",\n                    name: \"Fashion\",\n                    slug: \"fashion\"\n                },\n                rating: 4.1,\n                reviewCount: 456,\n                isFeatured: false,\n                tags: \"dress,summer,floral,casual,hm\"\n            },\n            // SPORTS & FITNESS (12 products)\n            {\n                id: \"5\",\n                name: \"Professional Basketball\",\n                slug: \"professional-basketball\",\n                description: \"Official size basketball\",\n                price: 34.99,\n                comparePrice: 45.00,\n                image: \"https://images.unsplash.com/photo-1546519638-68e109498ffc?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.4,\n                reviewCount: 92,\n                isFeatured: false,\n                tags: \"basketball,sports,official,grip,nike\"\n            },\n            {\n                id: \"27\",\n                name: \"Yoga Mat Premium\",\n                slug: \"yoga-mat-premium\",\n                description: \"Non-slip premium yoga mat\",\n                price: 39.99,\n                comparePrice: 55.00,\n                image: \"https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.2,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"yoga,mat,premium,non-slip,fitness\"\n            },\n            {\n                id: \"28\",\n                name: \"Nike Training Shoes\",\n                slug: \"nike-training-shoes\",\n                description: \"Cross-training shoes for gym workouts\",\n                price: 119.99,\n                comparePrice: 140.00,\n                image: \"https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.6,\n                reviewCount: 456,\n                isFeatured: true,\n                tags: \"training,shoes,nike,gym,cross-training\"\n            },\n            {\n                id: \"29\",\n                name: \"Adidas Soccer Ball\",\n                slug: \"adidas-soccer-ball\",\n                description: \"FIFA-approved soccer ball\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1614632537190-23e4b2e69c88?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.5,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"soccer,ball,adidas,fifa,football\"\n            },\n            {\n                id: \"30\",\n                name: \"Under Armour Gym Bag\",\n                slug: \"under-armour-gym-bag\",\n                description: \"Durable gym bag with multiple compartments\",\n                price: 49.99,\n                comparePrice: 69.99,\n                image: \"https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.3,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"gym,bag,under-armour,durable,sports\"\n            },\n            {\n                id: \"31\",\n                name: \"Puma Running Shorts\",\n                slug: \"puma-running-shorts\",\n                description: \"Lightweight running shorts with built-in brief\",\n                price: 24.99,\n                comparePrice: 34.99,\n                image: \"https://images.unsplash.com/photo-1506629905607-d9c297d3d45b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.1,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"running,shorts,puma,lightweight,athletic\"\n            },\n            {\n                id: \"32\",\n                name: \"Reebok Dumbbells Set\",\n                slug: \"reebok-dumbbells-set\",\n                description: \"Adjustable dumbbells for home workouts\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.7,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"dumbbells,weights,reebok,adjustable,home-gym\"\n            },\n            {\n                id: \"33\",\n                name: \"Nike Swim Goggles\",\n                slug: \"nike-swim-goggles\",\n                description: \"Anti-fog swimming goggles\",\n                price: 19.99,\n                comparePrice: 29.99,\n                image: \"https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.0,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"swimming,goggles,nike,anti-fog,water-sports\"\n            },\n            {\n                id: \"34\",\n                name: \"Adidas Tennis Racket\",\n                slug: \"adidas-tennis-racket\",\n                description: \"Professional tennis racket with carbon frame\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.4,\n                reviewCount: 78,\n                isFeatured: false,\n                tags: \"tennis,racket,adidas,professional,carbon\"\n            },\n            {\n                id: \"35\",\n                name: \"Under Armour Compression Shirt\",\n                slug: \"under-armour-compression-shirt\",\n                description: \"Moisture-wicking compression shirt\",\n                price: 39.99,\n                comparePrice: 54.99,\n                image: \"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.2,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"compression,shirt,under-armour,moisture-wicking,athletic\"\n            },\n            {\n                id: \"36\",\n                name: \"Puma Football Cleats\",\n                slug: \"puma-football-cleats\",\n                description: \"High-performance football cleats\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.5,\n                reviewCount: 156,\n                isFeatured: false,\n                tags: \"football,cleats,puma,performance,sports\"\n            },\n            {\n                id: \"37\",\n                name: \"Reebok Resistance Bands\",\n                slug: \"reebok-resistance-bands\",\n                description: \"Set of resistance bands for strength training\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"sports\",\n                    name: \"Sports & Fitness\",\n                    slug: \"sports\"\n                },\n                rating: 4.3,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"resistance,bands,reebok,strength,training\"\n            },\n            // HOME & GARDEN (12 products)\n            {\n                id: \"6\",\n                name: \"Smart LED Light Bulb\",\n                slug: \"smart-led-light-bulb\",\n                description: \"WiFi-enabled smart LED bulb\",\n                price: 24.99,\n                comparePrice: 35.00,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.2,\n                reviewCount: 67,\n                isFeatured: false,\n                tags: \"smart,led,bulb,wifi,color,philips\"\n            },\n            {\n                id: \"38\",\n                name: \"IKEA Bookshelf\",\n                slug: \"ikea-bookshelf\",\n                description: \"Modern wooden bookshelf with 5 shelves\",\n                price: 79.99,\n                comparePrice: 99.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.4,\n                reviewCount: 234,\n                isFeatured: false,\n                tags: \"bookshelf,furniture,ikea,wooden,storage\"\n            },\n            {\n                id: \"39\",\n                name: \"Philips Air Purifier\",\n                slug: \"philips-air-purifier\",\n                description: \"HEPA air purifier for large rooms\",\n                price: 299.99,\n                comparePrice: 349.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.6,\n                reviewCount: 156,\n                isFeatured: true,\n                tags: \"air-purifier,philips,hepa,clean-air,home\"\n            },\n            {\n                id: \"40\",\n                name: \"Wayfair Dining Table\",\n                slug: \"wayfair-dining-table\",\n                description: \"Solid wood dining table for 6 people\",\n                price: 599.99,\n                comparePrice: 749.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.5,\n                reviewCount: 89,\n                isFeatured: true,\n                tags: \"dining-table,furniture,wayfair,wood,family\"\n            },\n            {\n                id: \"41\",\n                name: \"Pottery Barn Throw Pillows\",\n                slug: \"pottery-barn-throw-pillows\",\n                description: \"Set of 4 decorative throw pillows\",\n                price: 89.99,\n                comparePrice: 119.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.3,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"pillows,decorative,pottery-barn,home-decor,comfort\"\n            },\n            {\n                id: \"42\",\n                name: \"West Elm Coffee Table\",\n                slug: \"west-elm-coffee-table\",\n                description: \"Mid-century modern coffee table\",\n                price: 399.99,\n                comparePrice: 499.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.4,\n                reviewCount: 123,\n                isFeatured: false,\n                tags: \"coffee-table,furniture,west-elm,mid-century,modern\"\n            },\n            {\n                id: \"43\",\n                name: \"IKEA Kitchen Cabinet\",\n                slug: \"ikea-kitchen-cabinet\",\n                description: \"Modular kitchen cabinet with soft-close doors\",\n                price: 149.99,\n                comparePrice: 189.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.2,\n                reviewCount: 345,\n                isFeatured: false,\n                tags: \"kitchen,cabinet,ikea,modular,storage\"\n            },\n            {\n                id: \"44\",\n                name: \"Philips Smart Thermostat\",\n                slug: \"philips-smart-thermostat\",\n                description: \"WiFi-enabled programmable thermostat\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.5,\n                reviewCount: 234,\n                isFeatured: true,\n                tags: \"thermostat,smart,philips,wifi,energy-saving\"\n            },\n            {\n                id: \"45\",\n                name: \"Wayfair Garden Hose\",\n                slug: \"wayfair-garden-hose\",\n                description: \"50ft expandable garden hose with spray nozzle\",\n                price: 39.99,\n                comparePrice: 59.99,\n                image: \"https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.1,\n                reviewCount: 456,\n                isFeatured: false,\n                tags: \"garden,hose,wayfair,expandable,outdoor\"\n            },\n            {\n                id: \"46\",\n                name: \"Pottery Barn Area Rug\",\n                slug: \"pottery-barn-area-rug\",\n                description: \"Hand-woven area rug in neutral colors\",\n                price: 299.99,\n                comparePrice: 399.99,\n                image: \"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.6,\n                reviewCount: 78,\n                isFeatured: false,\n                tags: \"rug,area-rug,pottery-barn,hand-woven,home-decor\"\n            },\n            {\n                id: \"47\",\n                name: \"West Elm Floor Lamp\",\n                slug: \"west-elm-floor-lamp\",\n                description: \"Modern brass floor lamp with fabric shade\",\n                price: 199.99,\n                comparePrice: 249.99,\n                image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.3,\n                reviewCount: 167,\n                isFeatured: false,\n                tags: \"lamp,floor-lamp,west-elm,brass,lighting\"\n            },\n            {\n                id: \"48\",\n                name: \"IKEA Plant Pot Set\",\n                slug: \"ikea-plant-pot-set\",\n                description: \"Set of 3 ceramic plant pots with drainage\",\n                price: 29.99,\n                comparePrice: 39.99,\n                image: \"https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=400&h=400&fit=crop\",\n                category: {\n                    id: \"home\",\n                    name: \"Home & Garden\",\n                    slug: \"home\"\n                },\n                rating: 4.0,\n                reviewCount: 567,\n                isFeatured: false,\n                tags: \"plant-pot,ceramic,ikea,drainage,gardening\"\n            }\n        ];\n        let filteredProducts = [\n            ...mockProducts\n        ];\n        // Apply filters\n        if (query.category) {\n            filteredProducts = filteredProducts.filter((p)=>p.category.slug === query.category);\n        }\n        if (query.search) {\n            const searchLower = query.search.toLowerCase();\n            filteredProducts = filteredProducts.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower) || p.tags.toLowerCase().includes(searchLower));\n        }\n        if (query.minPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price >= parseFloat(query.minPrice));\n        }\n        if (query.maxPrice) {\n            filteredProducts = filteredProducts.filter((p)=>p.price <= parseFloat(query.maxPrice));\n        }\n        // Apply sorting\n        if (query.sortBy === \"price\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.price - b.price : b.price - a.price);\n        } else if (query.sortBy === \"name\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name));\n        } else if (query.sortBy === \"rating\") {\n            filteredProducts.sort((a, b)=>query.sortOrder === \"asc\" ? a.rating - b.rating : b.rating - a.rating);\n        }\n        // Apply pagination\n        const page = parseInt(query.page);\n        const limit = parseInt(query.limit);\n        const skip = (page - 1) * limit;\n        const total = filteredProducts.length;\n        const paginatedProducts = filteredProducts.slice(skip, skip + limit);\n        const totalPages = Math.ceil(total / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            products: paginatedProducts,\n            pagination: {\n                page,\n                limit,\n                total,\n                totalPages,\n                hasNext: page < totalPages,\n                hasPrev: page > 1\n            }\n        });\n    } catch (error) {\n        console.error(\"Products API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        // This would be protected by admin middleware in a real app\n        const body = await req.json();\n        const product = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.product.create({\n            data: {\n                ...body,\n                slug: body.slug || body.name.toLowerCase().replace(/\\s+/g, \"-\")\n            },\n            include: {\n                category: true,\n                images: true\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(product, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Create product error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3Byb2R1Y3RzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXVEO0FBQzFCO0FBQ047QUFFdkIsTUFBTUcscUJBQXFCRCx5Q0FBUSxDQUFDO0lBQ2xDRyxNQUFNSCx5Q0FBUSxHQUFHSyxRQUFRLEdBQUdDLE9BQU8sQ0FBQztJQUNwQ0MsT0FBT1AseUNBQVEsR0FBR0ssUUFBUSxHQUFHQyxPQUFPLENBQUM7SUFDckNFLFVBQVVSLHlDQUFRLEdBQUdLLFFBQVE7SUFDN0JJLFFBQVFULHlDQUFRLEdBQUdLLFFBQVE7SUFDM0JLLFFBQVFWLDBDQUFNLENBQUM7UUFBQztRQUFRO1FBQVM7UUFBYTtLQUFTLEVBQUVLLFFBQVEsR0FBR0MsT0FBTyxDQUFDO0lBQzVFTSxXQUFXWiwwQ0FBTSxDQUFDO1FBQUM7UUFBTztLQUFPLEVBQUVLLFFBQVEsR0FBR0MsT0FBTyxDQUFDO0lBQ3RETyxVQUFVYix5Q0FBUSxHQUFHSyxRQUFRO0lBQzdCUyxVQUFVZCx5Q0FBUSxHQUFHSyxRQUFRO0FBQy9CO0FBRU8sZUFBZVUsSUFBSUMsR0FBZ0I7SUFDeEMsSUFBSTtRQUNGLE1BQU0sRUFBRUMsWUFBWSxFQUFFLEdBQUcsSUFBSUMsSUFBSUYsSUFBSUcsR0FBRztRQUN4QyxNQUFNQyxRQUFRbkIsbUJBQW1Cb0IsS0FBSyxDQUFDQyxPQUFPQyxXQUFXLENBQUNOO1FBRTFELHlEQUF5RDtRQUN6RCxNQUFNTyxlQUFlO1lBQ25CLDRCQUE0QjtZQUM1QjtnQkFDRUMsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFlQyxNQUFNO29CQUFlQyxNQUFNO2dCQUFjO2dCQUN4RUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBZUMsTUFBTTtvQkFBZUMsTUFBTTtnQkFBYztnQkFDeEVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQWVDLE1BQU07b0JBQWVDLE1BQU07Z0JBQWM7Z0JBQ3hFSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFlQyxNQUFNO29CQUFlQyxNQUFNO2dCQUFjO2dCQUN4RUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBZUMsTUFBTTtvQkFBZUMsTUFBTTtnQkFBYztnQkFDeEVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQWVDLE1BQU07b0JBQWVDLE1BQU07Z0JBQWM7Z0JBQ3hFSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFlQyxNQUFNO29CQUFlQyxNQUFNO2dCQUFjO2dCQUN4RUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBZUMsTUFBTTtvQkFBZUMsTUFBTTtnQkFBYztnQkFDeEVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQWVDLE1BQU07b0JBQWVDLE1BQU07Z0JBQWM7Z0JBQ3hFSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFlQyxNQUFNO29CQUFlQyxNQUFNO2dCQUFjO2dCQUN4RUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBZUMsTUFBTTtvQkFBZUMsTUFBTTtnQkFBYztnQkFDeEVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQWVDLE1BQU07b0JBQWVDLE1BQU07Z0JBQWM7Z0JBQ3hFSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFFQSx3QkFBd0I7WUFDeEI7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBV0MsTUFBTTtvQkFBV0MsTUFBTTtnQkFBVTtnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVdDLE1BQU07b0JBQVdDLE1BQU07Z0JBQVU7Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFXQyxNQUFNO29CQUFXQyxNQUFNO2dCQUFVO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBV0MsTUFBTTtvQkFBV0MsTUFBTTtnQkFBVTtnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVdDLE1BQU07b0JBQVdDLE1BQU07Z0JBQVU7Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFXQyxNQUFNO29CQUFXQyxNQUFNO2dCQUFVO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBV0MsTUFBTTtvQkFBV0MsTUFBTTtnQkFBVTtnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVdDLE1BQU07b0JBQVdDLE1BQU07Z0JBQVU7Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFXQyxNQUFNO29CQUFXQyxNQUFNO2dCQUFVO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBV0MsTUFBTTtvQkFBV0MsTUFBTTtnQkFBVTtnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVdDLE1BQU07b0JBQVdDLE1BQU07Z0JBQVU7Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFXQyxNQUFNO29CQUFXQyxNQUFNO2dCQUFVO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBRUEsaUNBQWlDO1lBQ2pDO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVVDLE1BQU07b0JBQW9CQyxNQUFNO2dCQUFTO2dCQUNuRUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBVUMsTUFBTTtvQkFBb0JDLE1BQU07Z0JBQVM7Z0JBQ25FSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFVQyxNQUFNO29CQUFvQkMsTUFBTTtnQkFBUztnQkFDbkVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVVDLE1BQU07b0JBQW9CQyxNQUFNO2dCQUFTO2dCQUNuRUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBVUMsTUFBTTtvQkFBb0JDLE1BQU07Z0JBQVM7Z0JBQ25FSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFVQyxNQUFNO29CQUFvQkMsTUFBTTtnQkFBUztnQkFDbkVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVVDLE1BQU07b0JBQW9CQyxNQUFNO2dCQUFTO2dCQUNuRUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBVUMsTUFBTTtvQkFBb0JDLE1BQU07Z0JBQVM7Z0JBQ25FSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFVQyxNQUFNO29CQUFvQkMsTUFBTTtnQkFBUztnQkFDbkVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVVDLE1BQU07b0JBQW9CQyxNQUFNO2dCQUFTO2dCQUNuRUssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBVUMsTUFBTTtvQkFBb0JDLE1BQU07Z0JBQVM7Z0JBQ25FSyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFVQyxNQUFNO29CQUFvQkMsTUFBTTtnQkFBUztnQkFDbkVLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUVBLDhCQUE4QjtZQUM5QjtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFRQyxNQUFNO29CQUFpQkMsTUFBTTtnQkFBTztnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVFDLE1BQU07b0JBQWlCQyxNQUFNO2dCQUFPO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBUUMsTUFBTTtvQkFBaUJDLE1BQU07Z0JBQU87Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFRQyxNQUFNO29CQUFpQkMsTUFBTTtnQkFBTztnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVFDLE1BQU07b0JBQWlCQyxNQUFNO2dCQUFPO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBUUMsTUFBTTtvQkFBaUJDLE1BQU07Z0JBQU87Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFRQyxNQUFNO29CQUFpQkMsTUFBTTtnQkFBTztnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVFDLE1BQU07b0JBQWlCQyxNQUFNO2dCQUFPO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBUUMsTUFBTTtvQkFBaUJDLE1BQU07Z0JBQU87Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7WUFDQTtnQkFDRVYsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsTUFBTTtnQkFDTkMsYUFBYTtnQkFDYkMsT0FBTztnQkFDUEMsY0FBYztnQkFDZEMsT0FBTztnQkFDUHZCLFVBQVU7b0JBQUVpQixJQUFJO29CQUFRQyxNQUFNO29CQUFpQkMsTUFBTTtnQkFBTztnQkFDNURLLFFBQVE7Z0JBQ1JDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLE1BQU07WUFDUjtZQUNBO2dCQUNFVixJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxPQUFPO2dCQUNQQyxjQUFjO2dCQUNkQyxPQUFPO2dCQUNQdkIsVUFBVTtvQkFBRWlCLElBQUk7b0JBQVFDLE1BQU07b0JBQWlCQyxNQUFNO2dCQUFPO2dCQUM1REssUUFBUTtnQkFDUkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VWLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE9BQU87Z0JBQ1BDLGNBQWM7Z0JBQ2RDLE9BQU87Z0JBQ1B2QixVQUFVO29CQUFFaUIsSUFBSTtvQkFBUUMsTUFBTTtvQkFBaUJDLE1BQU07Z0JBQU87Z0JBQzVESyxRQUFRO2dCQUNSQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxNQUFNO1lBQ1I7U0FDRDtRQUVELElBQUlDLG1CQUFtQjtlQUFJWjtTQUFhO1FBRXhDLGdCQUFnQjtRQUNoQixJQUFJSixNQUFNWixRQUFRLEVBQUU7WUFDbEI0QixtQkFBbUJBLGlCQUFpQkMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFOUIsUUFBUSxDQUFDbUIsSUFBSSxLQUFLUCxNQUFNWixRQUFRO1FBQ3BGO1FBRUEsSUFBSVksTUFBTVgsTUFBTSxFQUFFO1lBQ2hCLE1BQU04QixjQUFjbkIsTUFBTVgsTUFBTSxDQUFDK0IsV0FBVztZQUM1Q0osbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFDekNBLEVBQUVaLElBQUksQ0FBQ2MsV0FBVyxHQUFHQyxRQUFRLENBQUNGLGdCQUM5QkQsRUFBRVYsV0FBVyxDQUFDWSxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0YsZ0JBQ3JDRCxFQUFFSCxJQUFJLENBQUNLLFdBQVcsR0FBR0MsUUFBUSxDQUFDRjtRQUVsQztRQUVBLElBQUluQixNQUFNUCxRQUFRLEVBQUU7WUFDbEJ1QixtQkFBbUJBLGlCQUFpQkMsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFVCxLQUFLLElBQUlhLFdBQVd0QixNQUFNUCxRQUFRO1FBQ3RGO1FBRUEsSUFBSU8sTUFBTU4sUUFBUSxFQUFFO1lBQ2xCc0IsbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVQsS0FBSyxJQUFJYSxXQUFXdEIsTUFBTU4sUUFBUTtRQUN0RjtRQUVBLGdCQUFnQjtRQUNoQixJQUFJTSxNQUFNVixNQUFNLEtBQUssU0FBUztZQUM1QjBCLGlCQUFpQk8sSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQ3hCekIsTUFBTVIsU0FBUyxLQUFLLFFBQVFnQyxFQUFFZixLQUFLLEdBQUdnQixFQUFFaEIsS0FBSyxHQUFHZ0IsRUFBRWhCLEtBQUssR0FBR2UsRUFBRWYsS0FBSztRQUVyRSxPQUFPLElBQUlULE1BQU1WLE1BQU0sS0FBSyxRQUFRO1lBQ2xDMEIsaUJBQWlCTyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFDeEJ6QixNQUFNUixTQUFTLEtBQUssUUFBUWdDLEVBQUVsQixJQUFJLENBQUNvQixhQUFhLENBQUNELEVBQUVuQixJQUFJLElBQUltQixFQUFFbkIsSUFBSSxDQUFDb0IsYUFBYSxDQUFDRixFQUFFbEIsSUFBSTtRQUUxRixPQUFPLElBQUlOLE1BQU1WLE1BQU0sS0FBSyxVQUFVO1lBQ3BDMEIsaUJBQWlCTyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFDeEJ6QixNQUFNUixTQUFTLEtBQUssUUFBUWdDLEVBQUVaLE1BQU0sR0FBR2EsRUFBRWIsTUFBTSxHQUFHYSxFQUFFYixNQUFNLEdBQUdZLEVBQUVaLE1BQU07UUFFekU7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTTdCLE9BQU80QyxTQUFTM0IsTUFBTWpCLElBQUk7UUFDaEMsTUFBTUksUUFBUXdDLFNBQVMzQixNQUFNYixLQUFLO1FBQ2xDLE1BQU15QyxPQUFPLENBQUM3QyxPQUFPLEtBQUtJO1FBQzFCLE1BQU0wQyxRQUFRYixpQkFBaUJjLE1BQU07UUFDckMsTUFBTUMsb0JBQW9CZixpQkFBaUJnQixLQUFLLENBQUNKLE1BQU1BLE9BQU96QztRQUU5RCxNQUFNOEMsYUFBYUMsS0FBS0MsSUFBSSxDQUFDTixRQUFRMUM7UUFFckMsT0FBT1Qsa0ZBQVlBLENBQUMwRCxJQUFJLENBQUM7WUFDdkJDLFVBQVVOO1lBQ1ZPLFlBQVk7Z0JBQ1Z2RDtnQkFDQUk7Z0JBQ0EwQztnQkFDQUk7Z0JBQ0FNLFNBQVN4RCxPQUFPa0Q7Z0JBQ2hCTyxTQUFTekQsT0FBTztZQUNsQjtRQUNGO0lBQ0YsRUFBRSxPQUFPMEQsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPL0Qsa0ZBQVlBLENBQUMwRCxJQUFJLENBQ3RCO1lBQUVPLFNBQVM7UUFBd0IsR0FDbkM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFTyxlQUFlQyxLQUFLakQsR0FBZ0I7SUFDekMsSUFBSTtRQUNGLDREQUE0RDtRQUM1RCxNQUFNa0QsT0FBTyxNQUFNbEQsSUFBSXdDLElBQUk7UUFFM0IsTUFBTVcsVUFBVSxNQUFNcEUsdUNBQUVBLENBQUNvRSxPQUFPLENBQUNDLE1BQU0sQ0FBQztZQUN0Q0MsTUFBTTtnQkFDSixHQUFHSCxJQUFJO2dCQUNQdkMsTUFBTXVDLEtBQUt2QyxJQUFJLElBQUl1QyxLQUFLeEMsSUFBSSxDQUFDYyxXQUFXLEdBQUc4QixPQUFPLENBQUMsUUFBUTtZQUM3RDtZQUNBQyxTQUFTO2dCQUNQL0QsVUFBVTtnQkFDVmdFLFFBQVE7WUFDVjtRQUNGO1FBRUEsT0FBTzFFLGtGQUFZQSxDQUFDMEQsSUFBSSxDQUFDVyxTQUFTO1lBQUVILFFBQVE7UUFBSTtJQUNsRCxFQUFFLE9BQU9ILE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBTy9ELGtGQUFZQSxDQUFDMEQsSUFBSSxDQUN0QjtZQUFFTyxTQUFTO1FBQXdCLEdBQ25DO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWNvbW1lcmNlLWFwcC8uL2FwcC9hcGkvcHJvZHVjdHMvcm91dGUudHM/OTJhOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCJcbmltcG9ydCB7IGRiIH0gZnJvbSBcIkAvbGliL2RiXCJcbmltcG9ydCB7IHogfSBmcm9tIFwiem9kXCJcblxuY29uc3QgcHJvZHVjdFF1ZXJ5U2NoZW1hID0gei5vYmplY3Qoe1xuICBwYWdlOiB6LnN0cmluZygpLm9wdGlvbmFsKCkuZGVmYXVsdChcIjFcIiksXG4gIGxpbWl0OiB6LnN0cmluZygpLm9wdGlvbmFsKCkuZGVmYXVsdChcIjEyXCIpLFxuICBjYXRlZ29yeTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBzZWFyY2g6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgc29ydEJ5OiB6LmVudW0oW1wibmFtZVwiLCBcInByaWNlXCIsIFwiY3JlYXRlZEF0XCIsIFwicmF0aW5nXCJdKS5vcHRpb25hbCgpLmRlZmF1bHQoXCJjcmVhdGVkQXRcIiksXG4gIHNvcnRPcmRlcjogei5lbnVtKFtcImFzY1wiLCBcImRlc2NcIl0pLm9wdGlvbmFsKCkuZGVmYXVsdChcImRlc2NcIiksXG4gIG1pblByaWNlOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIG1heFByaWNlOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG59KVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcTogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXEudXJsKVxuICAgIGNvbnN0IHF1ZXJ5ID0gcHJvZHVjdFF1ZXJ5U2NoZW1hLnBhcnNlKE9iamVjdC5mcm9tRW50cmllcyhzZWFyY2hQYXJhbXMpKVxuXG4gICAgLy8gQ29tcHJlaGVuc2l2ZSBtb2NrIGRhdGEgd2l0aCAxMCsgcHJvZHVjdHMgcGVyIGNhdGVnb3J5XG4gICAgY29uc3QgbW9ja1Byb2R1Y3RzID0gW1xuICAgICAgLy8gRUxFQ1RST05JQ1MgKDEyIHByb2R1Y3RzKVxuICAgICAge1xuICAgICAgICBpZDogXCIxXCIsXG4gICAgICAgIG5hbWU6IFwiV2lyZWxlc3MgQmx1ZXRvb3RoIEhlYWRwaG9uZXNcIixcbiAgICAgICAgc2x1ZzogXCJ3aXJlbGVzcy1ibHVldG9vdGgtaGVhZHBob25lc1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJQcmVtaXVtIHdpcmVsZXNzIGhlYWRwaG9uZXMgd2l0aCBub2lzZSBjYW5jZWxsYXRpb25cIixcbiAgICAgICAgcHJpY2U6IDk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDE0OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDU3NDA0MjA5MjgtNWU1NjBjMDZkMzBlP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImVsZWN0cm9uaWNzXCIsIG5hbWU6IFwiRWxlY3Ryb25pY3NcIiwgc2x1ZzogXCJlbGVjdHJvbmljc1wiIH0sXG4gICAgICAgIHJhdGluZzogNC41LFxuICAgICAgICByZXZpZXdDb3VudDogMTI4LFxuICAgICAgICBpc0ZlYXR1cmVkOiB0cnVlLFxuICAgICAgICB0YWdzOiBcIndpcmVsZXNzLGJsdWV0b290aCxoZWFkcGhvbmVzLGF1ZGlvLGJvc2VcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjJcIixcbiAgICAgICAgbmFtZTogXCJTbWFydCBGaXRuZXNzIFdhdGNoXCIsXG4gICAgICAgIHNsdWc6IFwic21hcnQtZml0bmVzcy13YXRjaFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJBZHZhbmNlZCBmaXRuZXNzIHRyYWNraW5nIHNtYXJ0d2F0Y2hcIixcbiAgICAgICAgcHJpY2U6IDE5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAyOTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTIzMjc1MzM1Njg0LTM3ODk4YjZiYWYzMD93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJlbGVjdHJvbmljc1wiLCBuYW1lOiBcIkVsZWN0cm9uaWNzXCIsIHNsdWc6IFwiZWxlY3Ryb25pY3NcIiB9LFxuICAgICAgICByYXRpbmc6IDQuOCxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDg5LFxuICAgICAgICBpc0ZlYXR1cmVkOiB0cnVlLFxuICAgICAgICB0YWdzOiBcInNtYXJ0d2F0Y2gsZml0bmVzcyxoZWFsdGgsZ3BzLGFwcGxlXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI3XCIsXG4gICAgICAgIG5hbWU6IFwiaVBob25lIDE1IFByb1wiLFxuICAgICAgICBzbHVnOiBcImlwaG9uZS0xNS1wcm9cIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiTGF0ZXN0IGlQaG9uZSB3aXRoIGFkdmFuY2VkIGNhbWVyYSBzeXN0ZW1cIixcbiAgICAgICAgcHJpY2U6IDk5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxMDk5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU5Mjc1MDQ3NTMzOC03NGI3YjIxMDg1YWI/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjksXG4gICAgICAgIHJldmlld0NvdW50OiA0NTYsXG4gICAgICAgIGlzRmVhdHVyZWQ6IHRydWUsXG4gICAgICAgIHRhZ3M6IFwic21hcnRwaG9uZSxpcGhvbmUsYXBwbGUsY2FtZXJhLDVnXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI4XCIsXG4gICAgICAgIG5hbWU6IFwiU2Ftc3VuZyBHYWxheHkgUzI0XCIsXG4gICAgICAgIHNsdWc6IFwic2Ftc3VuZy1nYWxheHktczI0XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZsYWdzaGlwIEFuZHJvaWQgc21hcnRwaG9uZSB3aXRoIEFJIGZlYXR1cmVzXCIsXG4gICAgICAgIHByaWNlOiA4OTkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogOTk5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxMTcwNzE3MTYzNC01Zjg5N2ZmMDJhYTk/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjcsXG4gICAgICAgIHJldmlld0NvdW50OiAzMjQsXG4gICAgICAgIGlzRmVhdHVyZWQ6IHRydWUsXG4gICAgICAgIHRhZ3M6IFwic21hcnRwaG9uZSxzYW1zdW5nLGFuZHJvaWQsY2FtZXJhLGFpXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI5XCIsXG4gICAgICAgIG5hbWU6IFwiTWFjQm9vayBBaXIgTTNcIixcbiAgICAgICAgc2x1ZzogXCJtYWNib29rLWFpci1tM1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJVbHRyYS10aGluIGxhcHRvcCB3aXRoIE0zIGNoaXBcIixcbiAgICAgICAgcHJpY2U6IDEyOTkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMTM5OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MTczMzY3MTQ3MzEtNDg5Njg5ZmQxY2E4P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImVsZWN0cm9uaWNzXCIsIG5hbWU6IFwiRWxlY3Ryb25pY3NcIiwgc2x1ZzogXCJlbGVjdHJvbmljc1wiIH0sXG4gICAgICAgIHJhdGluZzogNC44LFxuICAgICAgICByZXZpZXdDb3VudDogMjM0LFxuICAgICAgICBpc0ZlYXR1cmVkOiB0cnVlLFxuICAgICAgICB0YWdzOiBcImxhcHRvcCxtYWNib29rLGFwcGxlLG0zLHVsdHJhYm9va1wiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiMTBcIixcbiAgICAgICAgbmFtZTogXCJTb255IFdILTEwMDBYTTVcIixcbiAgICAgICAgc2x1ZzogXCJzb255LXdoLTEwMDB4bTVcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiSW5kdXN0cnktbGVhZGluZyBub2lzZSBjYW5jZWxpbmcgaGVhZHBob25lc1wiLFxuICAgICAgICBwcmljZTogMzQ5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDM5OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0ODQ3MDQ4NDk3MDAtZjAzMmE1NjhlOTQ0P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImVsZWN0cm9uaWNzXCIsIG5hbWU6IFwiRWxlY3Ryb25pY3NcIiwgc2x1ZzogXCJlbGVjdHJvbmljc1wiIH0sXG4gICAgICAgIHJhdGluZzogNC42LFxuICAgICAgICByZXZpZXdDb3VudDogNTY3LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJoZWFkcGhvbmVzLHNvbnksbm9pc2UtY2FuY2VsaW5nLHdpcmVsZXNzXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIxMVwiLFxuICAgICAgICBuYW1lOiBcImlQYWQgUHJvIDEyLjlcIixcbiAgICAgICAgc2x1ZzogXCJpcGFkLXByby0xMi05XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlByb2Zlc3Npb25hbCB0YWJsZXQgd2l0aCBNMiBjaGlwXCIsXG4gICAgICAgIHByaWNlOiAxMDk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDExOTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ0MjQ0MDE1LTBkZjRiM2ZmYzZiMD93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJlbGVjdHJvbmljc1wiLCBuYW1lOiBcIkVsZWN0cm9uaWNzXCIsIHNsdWc6IFwiZWxlY3Ryb25pY3NcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNyxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDE4OSxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwidGFibGV0LGlwYWQsYXBwbGUsbTIscHJvZmVzc2lvbmFsXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIxMlwiLFxuICAgICAgICBuYW1lOiBcIk5pbnRlbmRvIFN3aXRjaCBPTEVEXCIsXG4gICAgICAgIHNsdWc6IFwibmludGVuZG8tc3dpdGNoLW9sZWRcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiR2FtaW5nIGNvbnNvbGUgd2l0aCB2aWJyYW50IE9MRUQgc2NyZWVuXCIsXG4gICAgICAgIHByaWNlOiAzNDkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMzc5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwNjE0NDA0MjYxNC1iMjQxN2U5OWM0ZTM/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjUsXG4gICAgICAgIHJldmlld0NvdW50OiA0NDUsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcImdhbWluZyxuaW50ZW5kbyxjb25zb2xlLG9sZWQscG9ydGFibGVcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjEzXCIsXG4gICAgICAgIG5hbWU6IFwiTEcgNTUgNEsgT0xFRCBUVlwiLFxuICAgICAgICBzbHVnOiBcImxnLTU1LTRrLW9sZWQtdHZcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiUHJlbWl1bSA0SyBPTEVEIHNtYXJ0IFRWXCIsXG4gICAgICAgIHByaWNlOiAxNDk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDE3OTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTkzMzU5Njc3ODc5LWE0YmI5MmY4MjlkMT93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJlbGVjdHJvbmljc1wiLCBuYW1lOiBcIkVsZWN0cm9uaWNzXCIsIHNsdWc6IFwiZWxlY3Ryb25pY3NcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDEyMyxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwidHYsb2xlZCw0ayxzbWFydCxsZ1wiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiMTRcIixcbiAgICAgICAgbmFtZTogXCJBaXJQb2RzIFBybyAyXCIsXG4gICAgICAgIHNsdWc6IFwiYWlycG9kcy1wcm8tMlwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJXaXJlbGVzcyBlYXJidWRzIHdpdGggYWRhcHRpdmUgdHJhbnNwYXJlbmN5XCIsXG4gICAgICAgIHByaWNlOiAyNDkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMjc5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwMDI5NDAzNzY4MS1jODBiNGNiNWI0MzQ/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjQsXG4gICAgICAgIHJldmlld0NvdW50OiA2NzgsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcImVhcmJ1ZHMsYWlycG9kcyxhcHBsZSx3aXJlbGVzcyxub2lzZS1jYW5jZWxpbmdcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjE1XCIsXG4gICAgICAgIG5hbWU6IFwiQ2Fub24gRU9TIFI1XCIsXG4gICAgICAgIHNsdWc6IFwiY2Fub24tZW9zLXI1XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlByb2Zlc3Npb25hbCBtaXJyb3JsZXNzIGNhbWVyYVwiLFxuICAgICAgICBwcmljZTogMzg5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA0MTk5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwNjk4MzM0MDEyNi05OWFiNGZlYWE2NGE/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjgsXG4gICAgICAgIHJldmlld0NvdW50OiA4OSxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiY2FtZXJhLGNhbm9uLG1pcnJvcmxlc3MscHJvZmVzc2lvbmFsLHBob3RvZ3JhcGh5XCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIxNlwiLFxuICAgICAgICBuYW1lOiBcIlRlc2xhIE1vZGVsIFMgUGxhaWRcIixcbiAgICAgICAgc2x1ZzogXCJ0ZXNsYS1tb2RlbC1zLXBsYWlkXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkVsZWN0cmljIHZlaGljbGUgd2l0aCBhdXRvcGlsb3RcIixcbiAgICAgICAgcHJpY2U6IDg5OTk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDk0OTk5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU2MDk1ODA4OS1iOGExOTI5Y2VhODk/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZWxlY3Ryb25pY3NcIiwgbmFtZTogXCJFbGVjdHJvbmljc1wiLCBzbHVnOiBcImVsZWN0cm9uaWNzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjksXG4gICAgICAgIHJldmlld0NvdW50OiA0NSxcbiAgICAgICAgaXNGZWF0dXJlZDogdHJ1ZSxcbiAgICAgICAgdGFnczogXCJlbGVjdHJpYyxjYXIsdGVzbGEsYXV0b3BpbG90LGx1eHVyeVwiLFxuICAgICAgfSxcblxuICAgICAgLy8gRkFTSElPTiAoMTIgcHJvZHVjdHMpXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjNcIixcbiAgICAgICAgbmFtZTogXCJCbHVlIFJ1bm5pbmcgU25lYWtlcnNcIixcbiAgICAgICAgc2x1ZzogXCJibHVlLXJ1bm5pbmctc25lYWtlcnNcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ29tZm9ydGFibGUgYmx1ZSBydW5uaW5nIHNuZWFrZXJzXCIsXG4gICAgICAgIHByaWNlOiA4OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxMjAuMDAsXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQyMjkxMDI2LTdlZWMyNjRjMjdmZj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuMyxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDU2LFxuICAgICAgICBpc0ZlYXR1cmVkOiB0cnVlLFxuICAgICAgICB0YWdzOiBcInNuZWFrZXJzLGJsdWUscnVubmluZyxzaG9lcyxzcG9ydHMsbmlrZVwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiNFwiLFxuICAgICAgICBuYW1lOiBcIkNsYXNzaWMgRGVuaW0gSmFja2V0XCIsXG4gICAgICAgIHNsdWc6IFwiY2xhc3NpYy1kZW5pbS1qYWNrZXRcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiVGltZWxlc3MgZGVuaW0gamFja2V0XCIsXG4gICAgICAgIHByaWNlOiA3OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxMTAuMDAsXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUxNjk4NjE4LTFkZmU1ZDk3ZDI1Nj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuNixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiZGVuaW0samFja2V0LGNsYXNzaWMsY290dG9uLGxldmlzXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIxN1wiLFxuICAgICAgICBuYW1lOiBcIk5pa2UgQWlyIE1heCAyNzBcIixcbiAgICAgICAgc2x1ZzogXCJuaWtlLWFpci1tYXgtMjcwXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkljb25pYyBsaWZlc3R5bGUgc25lYWtlcnMgd2l0aCBBaXIgTWF4IGN1c2hpb25pbmdcIixcbiAgICAgICAgcHJpY2U6IDEyOS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxNTAuMDAsXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ5Mjk4OTE2LWI0MWQ1MDFkMzc3Mj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuNSxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDc4OSxcbiAgICAgICAgaXNGZWF0dXJlZDogdHJ1ZSxcbiAgICAgICAgdGFnczogXCJzbmVha2VycyxuaWtlLGFpci1tYXgsbGlmZXN0eWxlLGN1c2hpb25pbmdcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjE4XCIsXG4gICAgICAgIG5hbWU6IFwiQWRpZGFzIFVsdHJhYm9vc3QgMjJcIixcbiAgICAgICAgc2x1ZzogXCJhZGlkYXMtdWx0cmFib29zdC0yMlwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJQcmVtaXVtIHJ1bm5pbmcgc2hvZXMgd2l0aCBCb29zdCB0ZWNobm9sb2d5XCIsXG4gICAgICAgIHByaWNlOiAxNzkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMjAwLjAwLFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwODIzMTM4NzA0Mi02NmQxNzczMDcwYTU/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZmFzaGlvblwiLCBuYW1lOiBcIkZhc2hpb25cIiwgc2x1ZzogXCJmYXNoaW9uXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjcsXG4gICAgICAgIHJldmlld0NvdW50OiA0NTYsXG4gICAgICAgIGlzRmVhdHVyZWQ6IHRydWUsXG4gICAgICAgIHRhZ3M6IFwic25lYWtlcnMsYWRpZGFzLHVsdHJhYm9vc3QscnVubmluZyxib29zdFwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiMTlcIixcbiAgICAgICAgbmFtZTogXCJMZXZpJ3MgNTAxIE9yaWdpbmFsIEplYW5zXCIsXG4gICAgICAgIHNsdWc6IFwibGV2aXMtNTAxLW9yaWdpbmFsLWplYW5zXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkNsYXNzaWMgc3RyYWlnaHQtbGVnIGplYW5zXCIsXG4gICAgICAgIHByaWNlOiA2OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA4OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDIyNzI2MDQtNzg3YzM4MzU1MzVkP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImZhc2hpb25cIiwgbmFtZTogXCJGYXNoaW9uXCIsIHNsdWc6IFwiZmFzaGlvblwiIH0sXG4gICAgICAgIHJhdGluZzogNC40LFxuICAgICAgICByZXZpZXdDb3VudDogMTIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiamVhbnMsbGV2aXMsNTAxLGNsYXNzaWMsZGVuaW1cIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjIwXCIsXG4gICAgICAgIG5hbWU6IFwiWmFyYSBXb29sIENvYXRcIixcbiAgICAgICAgc2x1ZzogXCJ6YXJhLXdvb2wtY29hdFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJFbGVnYW50IHdvb2wgY29hdCBmb3Igd2ludGVyXCIsXG4gICAgICAgIHByaWNlOiAxOTkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMjQ5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUzOTUzMzAxODQ0Ny02M2ZjY2UyNjc4ZTM/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZmFzaGlvblwiLCBuYW1lOiBcIkZhc2hpb25cIiwgc2x1ZzogXCJmYXNoaW9uXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjIsXG4gICAgICAgIHJldmlld0NvdW50OiAxNjcsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcImNvYXQsd29vbCx3aW50ZXIsZWxlZ2FudCx6YXJhXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIyMVwiLFxuICAgICAgICBuYW1lOiBcIkgmTSBDb3R0b24gVC1TaGlydFwiLFxuICAgICAgICBzbHVnOiBcImhtLWNvdHRvbi10LXNoaXJ0XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkJhc2ljIGNvdHRvbiB0LXNoaXJ0IGluIG11bHRpcGxlIGNvbG9yc1wiLFxuICAgICAgICBwcmljZTogMTIuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTIxNTcyMTYzNDc0LTY4NjRmOWNmMTdhYj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuMCxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNDUsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcInQtc2hpcnQsY290dG9uLGJhc2ljLGNhc3VhbCxobVwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiMjJcIixcbiAgICAgICAgbmFtZTogXCJOaWtlIERyaS1GSVQgU2hvcnRzXCIsXG4gICAgICAgIHNsdWc6IFwibmlrZS1kcmktZml0LXNob3J0c1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJBdGhsZXRpYyBzaG9ydHMgd2l0aCBtb2lzdHVyZS13aWNraW5nIHRlY2hub2xvZ3lcIixcbiAgICAgICAgcHJpY2U6IDM0Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDQ1LjAwLFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNjYyOTkwNTYwNy1kOWMyOTdkM2Q0NWI/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZmFzaGlvblwiLCBuYW1lOiBcIkZhc2hpb25cIiwgc2x1ZzogXCJmYXNoaW9uXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjMsXG4gICAgICAgIHJldmlld0NvdW50OiA1NjcsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcInNob3J0cyxhdGhsZXRpYyxuaWtlLGRyaS1maXQsc3BvcnRzXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIyM1wiLFxuICAgICAgICBuYW1lOiBcIkFkaWRhcyBUcmFjayBKYWNrZXRcIixcbiAgICAgICAgc2x1ZzogXCJhZGlkYXMtdHJhY2stamFja2V0XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkNsYXNzaWMgdGhyZWUtc3RyaXBlIHRyYWNrIGphY2tldFwiLFxuICAgICAgICBwcmljZTogNzkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogOTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTU2ODIxODQwLTNhNjNmOTU2MDlhNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuNSxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiamFja2V0LHRyYWNrLGFkaWRhcyx0aHJlZS1zdHJpcGUsYXRobGV0aWNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjI0XCIsXG4gICAgICAgIG5hbWU6IFwiWmFyYSBMZWF0aGVyIEhhbmRiYWdcIixcbiAgICAgICAgc2x1ZzogXCJ6YXJhLWxlYXRoZXItaGFuZGJhZ1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJQcmVtaXVtIGxlYXRoZXIgaGFuZGJhZyB3aXRoIGdvbGQgaGFyZHdhcmVcIixcbiAgICAgICAgcHJpY2U6IDE0OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxODkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUzMDYyNDA3LTk4ZWViNjRjNmE2Mj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJmYXNoaW9uXCIsIG5hbWU6IFwiRmFzaGlvblwiLCBzbHVnOiBcImZhc2hpb25cIiB9LFxuICAgICAgICByYXRpbmc6IDQuNixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDEyMyxcbiAgICAgICAgaXNGZWF0dXJlZDogdHJ1ZSxcbiAgICAgICAgdGFnczogXCJoYW5kYmFnLGxlYXRoZXIscHJlbWl1bSx6YXJhLGFjY2Vzc29yaWVzXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIyNVwiLFxuICAgICAgICBuYW1lOiBcIkxldmkncyBUcnVja2VyIEphY2tldFwiLFxuICAgICAgICBzbHVnOiBcImxldmlzLXRydWNrZXItamFja2V0XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkljb25pYyBkZW5pbSB0cnVja2VyIGphY2tldFwiLFxuICAgICAgICBwcmljZTogODkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMTE5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0NDk2NjUwMy03Y2M1YWM4ODJkNWY/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiZmFzaGlvblwiLCBuYW1lOiBcIkZhc2hpb25cIiwgc2x1ZzogXCJmYXNoaW9uXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjcsXG4gICAgICAgIHJldmlld0NvdW50OiAzNDUsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcImphY2tldCxkZW5pbSx0cnVja2VyLGxldmlzLGNsYXNzaWNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjI2XCIsXG4gICAgICAgIG5hbWU6IFwiSCZNIFN1bW1lciBEcmVzc1wiLFxuICAgICAgICBzbHVnOiBcImhtLXN1bW1lci1kcmVzc1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGbG93eSBzdW1tZXIgZHJlc3MgaW4gZmxvcmFsIHByaW50XCIsXG4gICAgICAgIHByaWNlOiAzOS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA1OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MTUzNzIwMzk3NDQtYjhmMDJhM2FlNDQ2P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImZhc2hpb25cIiwgbmFtZTogXCJGYXNoaW9uXCIsIHNsdWc6IFwiZmFzaGlvblwiIH0sXG4gICAgICAgIHJhdGluZzogNC4xLFxuICAgICAgICByZXZpZXdDb3VudDogNDU2LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJkcmVzcyxzdW1tZXIsZmxvcmFsLGNhc3VhbCxobVwiLFxuICAgICAgfSxcblxuICAgICAgLy8gU1BPUlRTICYgRklUTkVTUyAoMTIgcHJvZHVjdHMpXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjVcIixcbiAgICAgICAgbmFtZTogXCJQcm9mZXNzaW9uYWwgQmFza2V0YmFsbFwiLFxuICAgICAgICBzbHVnOiBcInByb2Zlc3Npb25hbC1iYXNrZXRiYWxsXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIk9mZmljaWFsIHNpemUgYmFza2V0YmFsbFwiLFxuICAgICAgICBwcmljZTogMzQuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogNDUuMDAsXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ2NTE5NjM4LTY4ZTEwOTQ5OGZmYz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJzcG9ydHNcIiwgbmFtZTogXCJTcG9ydHMgJiBGaXRuZXNzXCIsIHNsdWc6IFwic3BvcnRzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjQsXG4gICAgICAgIHJldmlld0NvdW50OiA5MixcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiYmFza2V0YmFsbCxzcG9ydHMsb2ZmaWNpYWwsZ3JpcCxuaWtlXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIyN1wiLFxuICAgICAgICBuYW1lOiBcIllvZ2EgTWF0IFByZW1pdW1cIixcbiAgICAgICAgc2x1ZzogXCJ5b2dhLW1hdC1wcmVtaXVtXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIk5vbi1zbGlwIHByZW1pdW0geW9nYSBtYXRcIixcbiAgICAgICAgcHJpY2U6IDM5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDU1LjAwLFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0NDM2NzU2Ny0wZjJmY2IwMDllMGI/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwic3BvcnRzXCIsIG5hbWU6IFwiU3BvcnRzICYgRml0bmVzc1wiLCBzbHVnOiBcInNwb3J0c1wiIH0sXG4gICAgICAgIHJhdGluZzogNC4yLFxuICAgICAgICByZXZpZXdDb3VudDogMjM0LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJ5b2dhLG1hdCxwcmVtaXVtLG5vbi1zbGlwLGZpdG5lc3NcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjI4XCIsXG4gICAgICAgIG5hbWU6IFwiTmlrZSBUcmFpbmluZyBTaG9lc1wiLFxuICAgICAgICBzbHVnOiBcIm5pa2UtdHJhaW5pbmctc2hvZXNcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ3Jvc3MtdHJhaW5pbmcgc2hvZXMgZm9yIGd5bSB3b3Jrb3V0c1wiLFxuICAgICAgICBwcmljZTogMTE5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDE0MC4wMCxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1OTU5NTA2NTMxMDYtNmM5ZWJkNjE0ZDNhP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcInNwb3J0c1wiLCBuYW1lOiBcIlNwb3J0cyAmIEZpdG5lc3NcIiwgc2x1ZzogXCJzcG9ydHNcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDQ1NixcbiAgICAgICAgaXNGZWF0dXJlZDogdHJ1ZSxcbiAgICAgICAgdGFnczogXCJ0cmFpbmluZyxzaG9lcyxuaWtlLGd5bSxjcm9zcy10cmFpbmluZ1wiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiMjlcIixcbiAgICAgICAgbmFtZTogXCJBZGlkYXMgU29jY2VyIEJhbGxcIixcbiAgICAgICAgc2x1ZzogXCJhZGlkYXMtc29jY2VyLWJhbGxcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRklGQS1hcHByb3ZlZCBzb2NjZXIgYmFsbFwiLFxuICAgICAgICBwcmljZTogMjkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMzkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjE0NjMyNTM3MTkwLTIzZTRiMmU2OWM4OD93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJzcG9ydHNcIiwgbmFtZTogXCJTcG9ydHMgJiBGaXRuZXNzXCIsIHNsdWc6IFwic3BvcnRzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjUsXG4gICAgICAgIHJldmlld0NvdW50OiAxMjMsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcInNvY2NlcixiYWxsLGFkaWRhcyxmaWZhLGZvb3RiYWxsXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIzMFwiLFxuICAgICAgICBuYW1lOiBcIlVuZGVyIEFybW91ciBHeW0gQmFnXCIsXG4gICAgICAgIHNsdWc6IFwidW5kZXItYXJtb3VyLWd5bS1iYWdcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRHVyYWJsZSBneW0gYmFnIHdpdGggbXVsdGlwbGUgY29tcGFydG1lbnRzXCIsXG4gICAgICAgIHByaWNlOiA0OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA2OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTMwNjI0MDctOThlZWI2NGM2YTYyP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcInNwb3J0c1wiLCBuYW1lOiBcIlNwb3J0cyAmIEZpdG5lc3NcIiwgc2x1ZzogXCJzcG9ydHNcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMyxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiZ3ltLGJhZyx1bmRlci1hcm1vdXIsZHVyYWJsZSxzcG9ydHNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjMxXCIsXG4gICAgICAgIG5hbWU6IFwiUHVtYSBSdW5uaW5nIFNob3J0c1wiLFxuICAgICAgICBzbHVnOiBcInB1bWEtcnVubmluZy1zaG9ydHNcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiTGlnaHR3ZWlnaHQgcnVubmluZyBzaG9ydHMgd2l0aCBidWlsdC1pbiBicmllZlwiLFxuICAgICAgICBwcmljZTogMjQuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMzQuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTA2NjI5OTA1NjA3LWQ5YzI5N2QzZDQ1Yj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJzcG9ydHNcIiwgbmFtZTogXCJTcG9ydHMgJiBGaXRuZXNzXCIsIHNsdWc6IFwic3BvcnRzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjEsXG4gICAgICAgIHJldmlld0NvdW50OiAzNDUsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcInJ1bm5pbmcsc2hvcnRzLHB1bWEsbGlnaHR3ZWlnaHQsYXRobGV0aWNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjMyXCIsXG4gICAgICAgIG5hbWU6IFwiUmVlYm9rIER1bWJiZWxscyBTZXRcIixcbiAgICAgICAgc2x1ZzogXCJyZWVib2stZHVtYmJlbGxzLXNldFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJBZGp1c3RhYmxlIGR1bWJiZWxscyBmb3IgaG9tZSB3b3Jrb3V0c1wiLFxuICAgICAgICBwcmljZTogMTk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDI0OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NzEwMTk2MTM0NTQtMWNiMmY5OWIyZDhiP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcInNwb3J0c1wiLCBuYW1lOiBcIlNwb3J0cyAmIEZpdG5lc3NcIiwgc2x1ZzogXCJzcG9ydHNcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNyxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDg5LFxuICAgICAgICBpc0ZlYXR1cmVkOiB0cnVlLFxuICAgICAgICB0YWdzOiBcImR1bWJiZWxscyx3ZWlnaHRzLHJlZWJvayxhZGp1c3RhYmxlLGhvbWUtZ3ltXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIzM1wiLFxuICAgICAgICBuYW1lOiBcIk5pa2UgU3dpbSBHb2dnbGVzXCIsXG4gICAgICAgIHNsdWc6IFwibmlrZS1zd2ltLWdvZ2dsZXNcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQW50aS1mb2cgc3dpbW1pbmcgZ29nZ2xlc1wiLFxuICAgICAgICBwcmljZTogMTkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMjkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTMwNTQ5Mzg3Nzg5LTRjMTAxNzI2NjYzNT93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJzcG9ydHNcIiwgbmFtZTogXCJTcG9ydHMgJiBGaXRuZXNzXCIsIHNsdWc6IFwic3BvcnRzXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjAsXG4gICAgICAgIHJldmlld0NvdW50OiAxNjcsXG4gICAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgICB0YWdzOiBcInN3aW1taW5nLGdvZ2dsZXMsbmlrZSxhbnRpLWZvZyx3YXRlci1zcG9ydHNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjM0XCIsXG4gICAgICAgIG5hbWU6IFwiQWRpZGFzIFRlbm5pcyBSYWNrZXRcIixcbiAgICAgICAgc2x1ZzogXCJhZGlkYXMtdGVubmlzLXJhY2tldFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJQcm9mZXNzaW9uYWwgdGVubmlzIHJhY2tldCB3aXRoIGNhcmJvbiBmcmFtZVwiLFxuICAgICAgICBwcmljZTogMTQ5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDE4OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MjIyNzk0NTc0ODYtNjJkY2M0YTQzMWQ2P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcInNwb3J0c1wiLCBuYW1lOiBcIlNwb3J0cyAmIEZpdG5lc3NcIiwgc2x1ZzogXCJzcG9ydHNcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNCxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDc4LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJ0ZW5uaXMscmFja2V0LGFkaWRhcyxwcm9mZXNzaW9uYWwsY2FyYm9uXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIzNVwiLFxuICAgICAgICBuYW1lOiBcIlVuZGVyIEFybW91ciBDb21wcmVzc2lvbiBTaGlydFwiLFxuICAgICAgICBzbHVnOiBcInVuZGVyLWFybW91ci1jb21wcmVzc2lvbi1zaGlydFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJNb2lzdHVyZS13aWNraW5nIGNvbXByZXNzaW9uIHNoaXJ0XCIsXG4gICAgICAgIHByaWNlOiAzOS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA1NC45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MjE1NzIxNjM0NzQtNjg2NGY5Y2YxN2FiP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcInNwb3J0c1wiLCBuYW1lOiBcIlNwb3J0cyAmIEZpdG5lc3NcIiwgc2x1ZzogXCJzcG9ydHNcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiY29tcHJlc3Npb24sc2hpcnQsdW5kZXItYXJtb3VyLG1vaXN0dXJlLXdpY2tpbmcsYXRobGV0aWNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjM2XCIsXG4gICAgICAgIG5hbWU6IFwiUHVtYSBGb290YmFsbCBDbGVhdHNcIixcbiAgICAgICAgc2x1ZzogXCJwdW1hLWZvb3RiYWxsLWNsZWF0c1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJIaWdoLXBlcmZvcm1hbmNlIGZvb3RiYWxsIGNsZWF0c1wiLFxuICAgICAgICBwcmljZTogODkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMTE5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3ODY2Mjk5NjQ0Mi00OGY2MDEwM2ZjOTY/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwic3BvcnRzXCIsIG5hbWU6IFwiU3BvcnRzICYgRml0bmVzc1wiLCBzbHVnOiBcInNwb3J0c1wiIH0sXG4gICAgICAgIHJhdGluZzogNC41LFxuICAgICAgICByZXZpZXdDb3VudDogMTU2LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJmb290YmFsbCxjbGVhdHMscHVtYSxwZXJmb3JtYW5jZSxzcG9ydHNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjM3XCIsXG4gICAgICAgIG5hbWU6IFwiUmVlYm9rIFJlc2lzdGFuY2UgQmFuZHNcIixcbiAgICAgICAgc2x1ZzogXCJyZWVib2stcmVzaXN0YW5jZS1iYW5kc1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTZXQgb2YgcmVzaXN0YW5jZSBiYW5kcyBmb3Igc3RyZW5ndGggdHJhaW5pbmdcIixcbiAgICAgICAgcHJpY2U6IDI5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDM5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU3MTAxOTYxMzQ1NC0xY2IyZjk5YjJkOGI/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwic3BvcnRzXCIsIG5hbWU6IFwiU3BvcnRzICYgRml0bmVzc1wiLCBzbHVnOiBcInNwb3J0c1wiIH0sXG4gICAgICAgIHJhdGluZzogNC4zLFxuICAgICAgICByZXZpZXdDb3VudDogMzQ1LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJyZXNpc3RhbmNlLGJhbmRzLHJlZWJvayxzdHJlbmd0aCx0cmFpbmluZ1wiLFxuICAgICAgfSxcblxuICAgICAgLy8gSE9NRSAmIEdBUkRFTiAoMTIgcHJvZHVjdHMpXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjZcIixcbiAgICAgICAgbmFtZTogXCJTbWFydCBMRUQgTGlnaHQgQnVsYlwiLFxuICAgICAgICBzbHVnOiBcInNtYXJ0LWxlZC1saWdodC1idWxiXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIldpRmktZW5hYmxlZCBzbWFydCBMRUQgYnVsYlwiLFxuICAgICAgICBwcmljZTogMjQuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMzUuMDAsXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTU4NjE4NjY2LWZjZDI1Yzg1Y2Q2ND93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDY3LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJzbWFydCxsZWQsYnVsYix3aWZpLGNvbG9yLHBoaWxpcHNcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjM4XCIsXG4gICAgICAgIG5hbWU6IFwiSUtFQSBCb29rc2hlbGZcIixcbiAgICAgICAgc2x1ZzogXCJpa2VhLWJvb2tzaGVsZlwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJNb2Rlcm4gd29vZGVuIGJvb2tzaGVsZiB3aXRoIDUgc2hlbHZlc1wiLFxuICAgICAgICBwcmljZTogNzkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogOTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg2MDIzNDkyMTI1LTI3YjJjMDQ1ZWZkNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNCxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiYm9va3NoZWxmLGZ1cm5pdHVyZSxpa2VhLHdvb2RlbixzdG9yYWdlXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCIzOVwiLFxuICAgICAgICBuYW1lOiBcIlBoaWxpcHMgQWlyIFB1cmlmaWVyXCIsXG4gICAgICAgIHNsdWc6IFwicGhpbGlwcy1haXItcHVyaWZpZXJcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiSEVQQSBhaXIgcHVyaWZpZXIgZm9yIGxhcmdlIHJvb21zXCIsXG4gICAgICAgIHByaWNlOiAyOTkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogMzQ5Ljk5LFxuICAgICAgICBpbWFnZTogXCJodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU1ODYxODY2Ni1mY2QyNWM4NWNkNjQ/dz00MDAmaD00MDAmZml0PWNyb3BcIixcbiAgICAgICAgY2F0ZWdvcnk6IHsgaWQ6IFwiaG9tZVwiLCBuYW1lOiBcIkhvbWUgJiBHYXJkZW5cIiwgc2x1ZzogXCJob21lXCIgfSxcbiAgICAgICAgcmF0aW5nOiA0LjYsXG4gICAgICAgIHJldmlld0NvdW50OiAxNTYsXG4gICAgICAgIGlzRmVhdHVyZWQ6IHRydWUsXG4gICAgICAgIHRhZ3M6IFwiYWlyLXB1cmlmaWVyLHBoaWxpcHMsaGVwYSxjbGVhbi1haXIsaG9tZVwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiNDBcIixcbiAgICAgICAgbmFtZTogXCJXYXlmYWlyIERpbmluZyBUYWJsZVwiLFxuICAgICAgICBzbHVnOiBcIndheWZhaXItZGluaW5nLXRhYmxlXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlNvbGlkIHdvb2QgZGluaW5nIHRhYmxlIGZvciA2IHBlb3BsZVwiLFxuICAgICAgICBwcmljZTogNTk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDc0OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1ODYwMjM0OTIxMjUtMjdiMmMwNDVlZmQ3P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImhvbWVcIiwgbmFtZTogXCJIb21lICYgR2FyZGVuXCIsIHNsdWc6IFwiaG9tZVwiIH0sXG4gICAgICAgIHJhdGluZzogNC41LFxuICAgICAgICByZXZpZXdDb3VudDogODksXG4gICAgICAgIGlzRmVhdHVyZWQ6IHRydWUsXG4gICAgICAgIHRhZ3M6IFwiZGluaW5nLXRhYmxlLGZ1cm5pdHVyZSx3YXlmYWlyLHdvb2QsZmFtaWx5XCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI0MVwiLFxuICAgICAgICBuYW1lOiBcIlBvdHRlcnkgQmFybiBUaHJvdyBQaWxsb3dzXCIsXG4gICAgICAgIHNsdWc6IFwicG90dGVyeS1iYXJuLXRocm93LXBpbGxvd3NcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiU2V0IG9mIDQgZGVjb3JhdGl2ZSB0aHJvdyBwaWxsb3dzXCIsXG4gICAgICAgIHByaWNlOiA4OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxMTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg2MDIzNDkyMTI1LTI3YjJjMDQ1ZWZkNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMyxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDE2NyxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwicGlsbG93cyxkZWNvcmF0aXZlLHBvdHRlcnktYmFybixob21lLWRlY29yLGNvbWZvcnRcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjQyXCIsXG4gICAgICAgIG5hbWU6IFwiV2VzdCBFbG0gQ29mZmVlIFRhYmxlXCIsXG4gICAgICAgIHNsdWc6IFwid2VzdC1lbG0tY29mZmVlLXRhYmxlXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIk1pZC1jZW50dXJ5IG1vZGVybiBjb2ZmZWUgdGFibGVcIixcbiAgICAgICAgcHJpY2U6IDM5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiA0OTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg2MDIzNDkyMTI1LTI3YjJjMDQ1ZWZkNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNCxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDEyMyxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiY29mZmVlLXRhYmxlLGZ1cm5pdHVyZSx3ZXN0LWVsbSxtaWQtY2VudHVyeSxtb2Rlcm5cIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjQzXCIsXG4gICAgICAgIG5hbWU6IFwiSUtFQSBLaXRjaGVuIENhYmluZXRcIixcbiAgICAgICAgc2x1ZzogXCJpa2VhLWtpdGNoZW4tY2FiaW5ldFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJNb2R1bGFyIGtpdGNoZW4gY2FiaW5ldCB3aXRoIHNvZnQtY2xvc2UgZG9vcnNcIixcbiAgICAgICAgcHJpY2U6IDE0OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAxODkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg2MDIzNDkyMTI1LTI3YjJjMDQ1ZWZkNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDM0NSxcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwia2l0Y2hlbixjYWJpbmV0LGlrZWEsbW9kdWxhcixzdG9yYWdlXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI0NFwiLFxuICAgICAgICBuYW1lOiBcIlBoaWxpcHMgU21hcnQgVGhlcm1vc3RhdFwiLFxuICAgICAgICBzbHVnOiBcInBoaWxpcHMtc21hcnQtdGhlcm1vc3RhdFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJXaUZpLWVuYWJsZWQgcHJvZ3JhbW1hYmxlIHRoZXJtb3N0YXRcIixcbiAgICAgICAgcHJpY2U6IDE5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAyNDkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTU4NjE4NjY2LWZjZDI1Yzg1Y2Q2ND93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNSxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDIzNCxcbiAgICAgICAgaXNGZWF0dXJlZDogdHJ1ZSxcbiAgICAgICAgdGFnczogXCJ0aGVybW9zdGF0LHNtYXJ0LHBoaWxpcHMsd2lmaSxlbmVyZ3ktc2F2aW5nXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICBpZDogXCI0NVwiLFxuICAgICAgICBuYW1lOiBcIldheWZhaXIgR2FyZGVuIEhvc2VcIixcbiAgICAgICAgc2x1ZzogXCJ3YXlmYWlyLWdhcmRlbi1ob3NlXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIjUwZnQgZXhwYW5kYWJsZSBnYXJkZW4gaG9zZSB3aXRoIHNwcmF5IG5venpsZVwiLFxuICAgICAgICBwcmljZTogMzkuOTksXG4gICAgICAgIGNvbXBhcmVQcmljZTogNTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDE2ODc5NTk1ODgyLTMzNzNhMDQ4MGI1Yj93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuMSxcbiAgICAgICAgcmV2aWV3Q291bnQ6IDQ1NixcbiAgICAgICAgaXNGZWF0dXJlZDogZmFsc2UsXG4gICAgICAgIHRhZ3M6IFwiZ2FyZGVuLGhvc2Usd2F5ZmFpcixleHBhbmRhYmxlLG91dGRvb3JcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjQ2XCIsXG4gICAgICAgIG5hbWU6IFwiUG90dGVyeSBCYXJuIEFyZWEgUnVnXCIsXG4gICAgICAgIHNsdWc6IFwicG90dGVyeS1iYXJuLWFyZWEtcnVnXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkhhbmQtd292ZW4gYXJlYSBydWcgaW4gbmV1dHJhbCBjb2xvcnNcIixcbiAgICAgICAgcHJpY2U6IDI5OS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAzOTkuOTksXG4gICAgICAgIGltYWdlOiBcImh0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTg2MDIzNDkyMTI1LTI3YjJjMDQ1ZWZkNz93PTQwMCZoPTQwMCZmaXQ9Y3JvcFwiLFxuICAgICAgICBjYXRlZ29yeTogeyBpZDogXCJob21lXCIsIG5hbWU6IFwiSG9tZSAmIEdhcmRlblwiLCBzbHVnOiBcImhvbWVcIiB9LFxuICAgICAgICByYXRpbmc6IDQuNixcbiAgICAgICAgcmV2aWV3Q291bnQ6IDc4LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJydWcsYXJlYS1ydWcscG90dGVyeS1iYXJuLGhhbmQtd292ZW4saG9tZS1kZWNvclwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IFwiNDdcIixcbiAgICAgICAgbmFtZTogXCJXZXN0IEVsbSBGbG9vciBMYW1wXCIsXG4gICAgICAgIHNsdWc6IFwid2VzdC1lbG0tZmxvb3ItbGFtcFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJNb2Rlcm4gYnJhc3MgZmxvb3IgbGFtcCB3aXRoIGZhYnJpYyBzaGFkZVwiLFxuICAgICAgICBwcmljZTogMTk5Ljk5LFxuICAgICAgICBjb21wYXJlUHJpY2U6IDI0OS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTg2MTg2NjYtZmNkMjVjODVjZDY0P3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImhvbWVcIiwgbmFtZTogXCJIb21lICYgR2FyZGVuXCIsIHNsdWc6IFwiaG9tZVwiIH0sXG4gICAgICAgIHJhdGluZzogNC4zLFxuICAgICAgICByZXZpZXdDb3VudDogMTY3LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJsYW1wLGZsb29yLWxhbXAsd2VzdC1lbG0sYnJhc3MsbGlnaHRpbmdcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiBcIjQ4XCIsXG4gICAgICAgIG5hbWU6IFwiSUtFQSBQbGFudCBQb3QgU2V0XCIsXG4gICAgICAgIHNsdWc6IFwiaWtlYS1wbGFudC1wb3Qtc2V0XCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlNldCBvZiAzIGNlcmFtaWMgcGxhbnQgcG90cyB3aXRoIGRyYWluYWdlXCIsXG4gICAgICAgIHByaWNlOiAyOS45OSxcbiAgICAgICAgY29tcGFyZVByaWNlOiAzOS45OSxcbiAgICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0ODU5NTU5MDAwMDYtMTBmNGQzMjRkNDExP3c9NDAwJmg9NDAwJmZpdD1jcm9wXCIsXG4gICAgICAgIGNhdGVnb3J5OiB7IGlkOiBcImhvbWVcIiwgbmFtZTogXCJIb21lICYgR2FyZGVuXCIsIHNsdWc6IFwiaG9tZVwiIH0sXG4gICAgICAgIHJhdGluZzogNC4wLFxuICAgICAgICByZXZpZXdDb3VudDogNTY3LFxuICAgICAgICBpc0ZlYXR1cmVkOiBmYWxzZSxcbiAgICAgICAgdGFnczogXCJwbGFudC1wb3QsY2VyYW1pYyxpa2VhLGRyYWluYWdlLGdhcmRlbmluZ1wiLFxuICAgICAgfSxcbiAgICBdXG5cbiAgICBsZXQgZmlsdGVyZWRQcm9kdWN0cyA9IFsuLi5tb2NrUHJvZHVjdHNdXG5cbiAgICAvLyBBcHBseSBmaWx0ZXJzXG4gICAgaWYgKHF1ZXJ5LmNhdGVnb3J5KSB7XG4gICAgICBmaWx0ZXJlZFByb2R1Y3RzID0gZmlsdGVyZWRQcm9kdWN0cy5maWx0ZXIocCA9PiBwLmNhdGVnb3J5LnNsdWcgPT09IHF1ZXJ5LmNhdGVnb3J5KVxuICAgIH1cblxuICAgIGlmIChxdWVyeS5zZWFyY2gpIHtcbiAgICAgIGNvbnN0IHNlYXJjaExvd2VyID0gcXVlcnkuc2VhcmNoLnRvTG93ZXJDYXNlKClcbiAgICAgIGZpbHRlcmVkUHJvZHVjdHMgPSBmaWx0ZXJlZFByb2R1Y3RzLmZpbHRlcihwID0+XG4gICAgICAgIHAubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBwLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XG4gICAgICAgIHAudGFncy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKVxuICAgICAgKVxuICAgIH1cblxuICAgIGlmIChxdWVyeS5taW5QcmljZSkge1xuICAgICAgZmlsdGVyZWRQcm9kdWN0cyA9IGZpbHRlcmVkUHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5wcmljZSA+PSBwYXJzZUZsb2F0KHF1ZXJ5Lm1pblByaWNlISkpXG4gICAgfVxuXG4gICAgaWYgKHF1ZXJ5Lm1heFByaWNlKSB7XG4gICAgICBmaWx0ZXJlZFByb2R1Y3RzID0gZmlsdGVyZWRQcm9kdWN0cy5maWx0ZXIocCA9PiBwLnByaWNlIDw9IHBhcnNlRmxvYXQocXVlcnkubWF4UHJpY2UhKSlcbiAgICB9XG5cbiAgICAvLyBBcHBseSBzb3J0aW5nXG4gICAgaWYgKHF1ZXJ5LnNvcnRCeSA9PT0gXCJwcmljZVwiKSB7XG4gICAgICBmaWx0ZXJlZFByb2R1Y3RzLnNvcnQoKGEsIGIpID0+XG4gICAgICAgIHF1ZXJ5LnNvcnRPcmRlciA9PT0gXCJhc2NcIiA/IGEucHJpY2UgLSBiLnByaWNlIDogYi5wcmljZSAtIGEucHJpY2VcbiAgICAgIClcbiAgICB9IGVsc2UgaWYgKHF1ZXJ5LnNvcnRCeSA9PT0gXCJuYW1lXCIpIHtcbiAgICAgIGZpbHRlcmVkUHJvZHVjdHMuc29ydCgoYSwgYikgPT5cbiAgICAgICAgcXVlcnkuc29ydE9yZGVyID09PSBcImFzY1wiID8gYS5uYW1lLmxvY2FsZUNvbXBhcmUoYi5uYW1lKSA6IGIubmFtZS5sb2NhbGVDb21wYXJlKGEubmFtZSlcbiAgICAgIClcbiAgICB9IGVsc2UgaWYgKHF1ZXJ5LnNvcnRCeSA9PT0gXCJyYXRpbmdcIikge1xuICAgICAgZmlsdGVyZWRQcm9kdWN0cy5zb3J0KChhLCBiKSA9PlxuICAgICAgICBxdWVyeS5zb3J0T3JkZXIgPT09IFwiYXNjXCIgPyBhLnJhdGluZyAtIGIucmF0aW5nIDogYi5yYXRpbmcgLSBhLnJhdGluZ1xuICAgICAgKVxuICAgIH1cblxuICAgIC8vIEFwcGx5IHBhZ2luYXRpb25cbiAgICBjb25zdCBwYWdlID0gcGFyc2VJbnQocXVlcnkucGFnZSlcbiAgICBjb25zdCBsaW1pdCA9IHBhcnNlSW50KHF1ZXJ5LmxpbWl0KVxuICAgIGNvbnN0IHNraXAgPSAocGFnZSAtIDEpICogbGltaXRcbiAgICBjb25zdCB0b3RhbCA9IGZpbHRlcmVkUHJvZHVjdHMubGVuZ3RoXG4gICAgY29uc3QgcGFnaW5hdGVkUHJvZHVjdHMgPSBmaWx0ZXJlZFByb2R1Y3RzLnNsaWNlKHNraXAsIHNraXAgKyBsaW1pdClcblxuICAgIGNvbnN0IHRvdGFsUGFnZXMgPSBNYXRoLmNlaWwodG90YWwgLyBsaW1pdClcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBwcm9kdWN0czogcGFnaW5hdGVkUHJvZHVjdHMsXG4gICAgICBwYWdpbmF0aW9uOiB7XG4gICAgICAgIHBhZ2UsXG4gICAgICAgIGxpbWl0LFxuICAgICAgICB0b3RhbCxcbiAgICAgICAgdG90YWxQYWdlcyxcbiAgICAgICAgaGFzTmV4dDogcGFnZSA8IHRvdGFsUGFnZXMsXG4gICAgICAgIGhhc1ByZXY6IHBhZ2UgPiAxLFxuICAgICAgfSxcbiAgICB9KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJQcm9kdWN0cyBBUEkgZXJyb3I6XCIsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgbWVzc2FnZTogXCJJbnRlcm5hbCBzZXJ2ZXIgZXJyb3JcIiB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcTogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICAvLyBUaGlzIHdvdWxkIGJlIHByb3RlY3RlZCBieSBhZG1pbiBtaWRkbGV3YXJlIGluIGEgcmVhbCBhcHBcbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxLmpzb24oKVxuICAgIFxuICAgIGNvbnN0IHByb2R1Y3QgPSBhd2FpdCBkYi5wcm9kdWN0LmNyZWF0ZSh7XG4gICAgICBkYXRhOiB7XG4gICAgICAgIC4uLmJvZHksXG4gICAgICAgIHNsdWc6IGJvZHkuc2x1ZyB8fCBib2R5Lm5hbWUudG9Mb3dlckNhc2UoKS5yZXBsYWNlKC9cXHMrL2csIFwiLVwiKSxcbiAgICAgIH0sXG4gICAgICBpbmNsdWRlOiB7XG4gICAgICAgIGNhdGVnb3J5OiB0cnVlLFxuICAgICAgICBpbWFnZXM6IHRydWUsXG4gICAgICB9LFxuICAgIH0pXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24ocHJvZHVjdCwgeyBzdGF0dXM6IDIwMSB9KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJDcmVhdGUgcHJvZHVjdCBlcnJvcjpcIiwgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBtZXNzYWdlOiBcIkludGVybmFsIHNlcnZlciBlcnJvclwiIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJkYiIsInoiLCJwcm9kdWN0UXVlcnlTY2hlbWEiLCJvYmplY3QiLCJwYWdlIiwic3RyaW5nIiwib3B0aW9uYWwiLCJkZWZhdWx0IiwibGltaXQiLCJjYXRlZ29yeSIsInNlYXJjaCIsInNvcnRCeSIsImVudW0iLCJzb3J0T3JkZXIiLCJtaW5QcmljZSIsIm1heFByaWNlIiwiR0VUIiwicmVxIiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwicXVlcnkiLCJwYXJzZSIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwibW9ja1Byb2R1Y3RzIiwiaWQiLCJuYW1lIiwic2x1ZyIsImRlc2NyaXB0aW9uIiwicHJpY2UiLCJjb21wYXJlUHJpY2UiLCJpbWFnZSIsInJhdGluZyIsInJldmlld0NvdW50IiwiaXNGZWF0dXJlZCIsInRhZ3MiLCJmaWx0ZXJlZFByb2R1Y3RzIiwiZmlsdGVyIiwicCIsInNlYXJjaExvd2VyIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInBhcnNlRmxvYXQiLCJzb3J0IiwiYSIsImIiLCJsb2NhbGVDb21wYXJlIiwicGFyc2VJbnQiLCJza2lwIiwidG90YWwiLCJsZW5ndGgiLCJwYWdpbmF0ZWRQcm9kdWN0cyIsInNsaWNlIiwidG90YWxQYWdlcyIsIk1hdGgiLCJjZWlsIiwianNvbiIsInByb2R1Y3RzIiwicGFnaW5hdGlvbiIsImhhc05leHQiLCJoYXNQcmV2IiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsInN0YXR1cyIsIlBPU1QiLCJib2R5IiwicHJvZHVjdCIsImNyZWF0ZSIsImRhdGEiLCJyZXBsYWNlIiwiaW5jbHVkZSIsImltYWdlcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/db.ts":
/*!*******************!*\
  !*** ./lib/db.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst db = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalForPrisma.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsS0FDWEYsZ0JBQWdCRyxNQUFNLElBQ3RCLElBQUlKLHdEQUFZQSxDQUFDO0lBQ2ZLLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUU7QUFFSixJQUFJQyxJQUF5QixFQUFjTCxnQkFBZ0JHLE1BQU0sR0FBR0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lY29tbWVyY2UtYXBwLy4vbGliL2RiLnRzPzFkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz9cbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ3F1ZXJ5J10sXG4gIH0pXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gZGJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwiZGIiLCJwcmlzbWEiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5COmkar%5CDocuments%5COmkar%5Cstart%5Cprojects%5Cmock%5Cecom&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();