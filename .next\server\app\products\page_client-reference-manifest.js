globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/products/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/home/<USER>":{"*":{"id":"(ssr)/./components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/chatbot/chatbot.tsx":{"*":{"id":"(ssr)/./components/chatbot/chatbot.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/footer.tsx":{"*":{"id":"(ssr)/./components/layout/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/layout/header.tsx":{"*":{"id":"(ssr)/./components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers.tsx":{"*":{"id":"(ssr)/./components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/product-filters.tsx":{"*":{"id":"(ssr)/./components/products/product-filters.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/product-grid.tsx":{"*":{"id":"(ssr)/./components/products/product-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/product-search.tsx":{"*":{"id":"(ssr)/./components/products/product-search.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/product-sort.tsx":{"*":{"id":"(ssr)/./components/products/product-sort.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\ai-recommendations.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\category-showcase.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\featured-products.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\hero-section.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\newsletter-signup.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\home\\trust-badges.tsx":{"id":"(app-pages-browser)/./components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\chatbot\\chatbot.tsx":{"id":"(app-pages-browser)/./components/chatbot/chatbot.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\layout\\footer.tsx":{"id":"(app-pages-browser)/./components/layout/footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\layout\\header.tsx":{"id":"(app-pages-browser)/./components/layout/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\providers.tsx":{"id":"(app-pages-browser)/./components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\products\\product-filters.tsx":{"id":"(app-pages-browser)/./components/products/product-filters.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\products\\product-grid.tsx":{"id":"(app-pages-browser)/./components/products/product-grid.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\products\\product-search.tsx":{"id":"(app-pages-browser)/./components/products/product-search.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\components\\products\\product-sort.tsx":{"id":"(app-pages-browser)/./components/products/product-sort.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\page":[],"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\not-found":[],"C:\\Users\\<USER>\\Documents\\Omkar\\start\\projects\\mock\\ecom\\app\\products\\page":[]}}