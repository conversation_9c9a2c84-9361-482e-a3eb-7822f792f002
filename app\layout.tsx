import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Toaster } from '@/components/ui/toaster'
import { ChatBot } from '@/components/chatbot/chatbot'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ECommerce App - Your Ultimate Shopping Destination',
  description: 'Discover amazing products with AI-powered recommendations, secure checkout, and exceptional customer service.',
  keywords: 'ecommerce, shopping, online store, AI recommendations, secure payment',
  authors: [{ name: 'ECommerce Team' }],
  creator: 'ECommerce App',
  publisher: 'ECommerce App',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    title: 'ECommerce App - Your Ultimate Shopping Destination',
    description: 'Discover amazing products with AI-powered recommendations, secure checkout, and exceptional customer service.',
    siteName: 'ECommerce App',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ECommerce App - Your Ultimate Shopping Destination',
    description: 'Discover amazing products with AI-powered recommendations, secure checkout, and exceptional customer service.',
    creator: '@ecommerceapp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
          <ChatBot />
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
