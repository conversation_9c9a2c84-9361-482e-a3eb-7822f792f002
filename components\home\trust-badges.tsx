"use client"

import { Shield, Truck, CreditCard, Headphones, Award, Lock } from "lucide-react"

const trustFeatures = [
  {
    icon: Shield,
    title: "Secure Shopping",
    description: "SSL encryption & PCI DSS compliance",
  },
  {
    icon: Truck,
    title: "Fast Delivery",
    description: "Free shipping on orders over $50",
  },
  {
    icon: CreditCard,
    title: "Secure Payments",
    description: "Multiple payment options available",
  },
  {
    icon: Headphones,
    title: "24/7 Support",
    description: "AI-powered customer assistance",
  },
  {
    icon: Award,
    title: "Quality Guarantee",
    description: "30-day money-back guarantee",
  },
  {
    icon: Lock,
    title: "Privacy Protected",
    description: "GDPR compliant data protection",
  },
]

export function TrustBadges() {
  return (
    <section className="container px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold tracking-tight mb-4">Why Choose Us?</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          We're committed to providing you with a safe, secure, and exceptional shopping experience
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {trustFeatures.map((feature, index) => (
          <div key={index} className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <feature.icon className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">256-bit</div>
          <div className="text-sm text-gray-600">SSL Encryption</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">PCI DSS</div>
          <div className="text-sm text-gray-600">Compliant</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">GDPR</div>
          <div className="text-sm text-gray-600">Protected</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">ISO 27001</div>
          <div className="text-sm text-gray-600">Certified</div>
        </div>
      </div>
    </section>
  )
}
