"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!************************************************!*\
  !*** ./components/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIRecommendations: function() { return /* binding */ AIRecommendations; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ AIRecommendations auto */ \n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Smart Recommendations\",\n        description: \"Our AI analyzes your preferences and browsing history to suggest products you'll love.\"\n    },\n    {\n        icon: _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Dynamic Pricing\",\n        description: \"Get the best deals with our machine learning-powered pricing optimization.\"\n    },\n    {\n        icon: _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Social Shopping\",\n        description: \"Discover what others like you are buying with collaborative filtering.\"\n    },\n    {\n        icon: _barrel_optimize_names_Brain_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Instant Insights\",\n        description: \"Real-time inventory updates and personalized shopping assistance.\"\n    }\n];\nfunction AIRecommendations() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-gray-50 py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold tracking-tight mb-4\",\n                            children: \"Powered by Artificial Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                            children: \"Experience the future of shopping with our AI-driven features that make finding the perfect products effortless and enjoyable.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                className: \"h-6 w-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-center text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"Ready to Experience AI-Powered Shopping?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100 mb-6 max-w-2xl mx-auto\",\n                            children: \"Join thousands of satisfied customers who have discovered the perfect products with our intelligent recommendation system.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/products\",\n                                        children: \"Start Shopping Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    className: \"border-white text-white hover:bg-white hover:text-blue-600\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/about\",\n                                        children: \"Learn More About Our AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Omkar\\\\start\\\\projects\\\\mock\\\\ecom\\\\components\\\\home\\\\ai-recommendations.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c = AIRecommendations;\nvar _c;\n$RefreshReg$(_c, \"AIRecommendations\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});