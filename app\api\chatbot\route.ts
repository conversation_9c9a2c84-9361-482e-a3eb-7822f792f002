import { NextRequest, NextResponse } from "next/server"
import { GoogleGenerative<PERSON>I } from "@google/generative-ai"

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "")

// Product database for context
const PRODUCT_CONTEXT = `
You are an AI shopping assistant for an ecommerce platform. Here are our current products:

ELECTRONICS (12 products):
- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation
- Smart Fitness Watch ($199.99) - Advanced fitness tracking
- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera
- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features
- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip
- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling
- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip
- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen
- LG 55" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV
- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency
- Canon EOS R5 ($3899.99) - Professional mirrorless camera
- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot

FASHION (12 products):
- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning
- Classic Denim Jacket ($79.99) - Timeless denim jacket
- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers
- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost
- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans
- Zara Wool Coat ($199.99) - Elegant wool coat for winter
- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors
- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking
- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket
- Zara Leather Handbag ($149.99) - Premium leather with gold hardware
- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket
- H&M Summer Dress ($39.99) - Flowy summer dress in floral print

SPORTS & FITNESS (12 products):
- Professional Basketball ($34.99) - Official size basketball
- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat
- Nike Training Shoes ($119.99) - Cross-training shoes for gym
- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball
- Under Armour Gym Bag ($49.99) - Durable with multiple compartments
- Puma Running Shorts ($24.99) - Lightweight with built-in brief
- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts
- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles
- Adidas Tennis Racket ($149.99) - Professional with carbon frame
- Under Armour Compression Shirt ($39.99) - Moisture-wicking
- Puma Football Cleats ($89.99) - High-performance football cleats
- Reebok Resistance Bands ($29.99) - Set for strength training

HOME & GARDEN (12 products):
- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb
- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves
- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms
- Wayfair Dining Table ($599.99) - Solid wood dining table for 6
- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows
- West Elm Coffee Table ($399.99) - Mid-century modern coffee table
- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors
- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable
- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle
- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors
- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade
- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage

STORE POLICIES:
- Free shipping on orders over $50
- 30-day return policy
- Standard shipping: 3-5 business days ($5.99)
- Express shipping: 1-2 business days ($12.99)
- International shipping available
- We accept all major credit cards, PayPal, Apple Pay, Google Pay
- Customer service: +****************
- Email: <EMAIL>
`

export async function POST(req: NextRequest) {
  try {
    const { message, sessionId } = await req.json()

    if (!message || !sessionId) {
      return NextResponse.json(
        { error: "Message and sessionId are required" },
        { status: 400 }
      )
    }

    // Check if Gemini API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.warn("GEMINI_API_KEY not found, using intelligent fallback responses")
      return getIntelligentFallbackResponse(message, sessionId)
    }

    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" })

      const prompt = `${PRODUCT_CONTEXT}

You are a helpful, friendly AI shopping assistant. The customer says: "${message}"

Guidelines:
1. Be conversational and helpful
2. Recommend specific products from our inventory with exact names and prices
3. Ask follow-up questions to better understand their needs
4. Provide helpful information about shipping, returns, and policies when relevant
5. If they ask about products we don't have, suggest similar alternatives from our catalog
6. Keep responses concise but informative (2-4 sentences max)
7. Use emojis sparingly for a friendly tone
8. Always try to guide them toward making a purchase or finding what they need
9. Format product recommendations clearly with bullet points when listing multiple items

Respond as a knowledgeable shopping assistant:`

      const result = await model.generateContent(prompt)
      const response = result.response.text()

      return NextResponse.json({
        response,
        sessionId,
      })
    } catch (aiError) {
      console.error("Gemini AI error:", aiError)
      // Fallback to predefined responses if AI fails
      return getIntelligentFallbackResponse(message, sessionId)
    }

  } catch (error) {
    console.error("Chatbot API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Intelligent fallback function for when AI is not available
function getIntelligentFallbackResponse(message: string, sessionId: string) {
  const lowerMessage = message.toLowerCase()
  let response = ""

  // Price-based queries
  if (lowerMessage.includes("under") || lowerMessage.includes("below") || lowerMessage.includes("less than")) {
    const priceMatch = lowerMessage.match(/\$?(\d+)/)
    const budget = priceMatch ? parseInt(priceMatch[1]) : 100

    if (lowerMessage.includes("headphones") || lowerMessage.includes("earbuds")) {
      if (budget >= 200) {
        response = `Great! For headphones under $${budget}, I recommend:

• **Wireless Bluetooth Headphones** - $99.99 ✨
  Premium wireless with noise cancellation - perfect choice!

• **Sony WH-1000XM5** - $349.99 (if budget allows)
  Industry-leading noise canceling headphones

• **AirPods Pro 2** - $249.99
  Wireless earbuds with adaptive transparency

The Wireless Bluetooth Headphones are our most popular choice in your budget range!`
      } else {
        response = `For headphones under $${budget}, I have a perfect match:

• **Wireless Bluetooth Headphones** - $99.99 ✨
  Premium wireless headphones with noise cancellation

This is our best-selling option and fits perfectly in your budget! Would you like to know more about the features?`
      }
    } else if (lowerMessage.includes("electronics") || lowerMessage.includes("tech")) {
      response = `Here are some great electronics under $${budget}:

• **Smart LED Light Bulb** - $24.99
  WiFi-enabled smart LED bulb with color changing

• **Nike Swim Goggles** - $19.99
  Anti-fog swimming goggles

${budget >= 100 ? '• **Wireless Bluetooth Headphones** - $99.99\n  Premium wireless headphones with noise cancellation' : ''}

What type of electronics are you most interested in?`
    } else {
      response = `I can help you find great products under $${budget}! Here are some popular options:

• **H&M Cotton T-Shirt** - $12.99 (Fashion)
• **Smart LED Light Bulb** - $24.99 (Home)
• **Adidas Soccer Ball** - $29.99 (Sports)
${budget >= 50 ? '• **Under Armour Gym Bag** - $49.99 (Sports)' : ''}
${budget >= 90 ? '• **Blue Running Sneakers** - $89.99 (Fashion)' : ''}

What category interests you most?`
    }
  } else if (lowerMessage.includes("sneakers") || lowerMessage.includes("shoes")) {
    response = `I found some great sneakers for you! Here are my top recommendations:

• **Blue Running Sneakers** - $89.99 ⭐
  Comfortable blue running sneakers with advanced cushioning

• **Nike Air Max 270** - $129.99
  Iconic lifestyle sneakers with Air Max cushioning

• **Adidas Ultraboost 22** - $179.99
  Premium running shoes with Boost technology

The Blue Running Sneakers are very popular! Would you like to see more details?`
  } else if (lowerMessage.includes("electronics") || lowerMessage.includes("tech") || lowerMessage.includes("gadgets")) {
    response = `Here are some popular electronics from our collection:

🎧 **Audio & Entertainment**
• **Wireless Bluetooth Headphones** - $99.99
• **Sony WH-1000XM5** - $349.99
• **AirPods Pro 2** - $249.99

📱 **Mobile & Computing**
• **iPhone 15 Pro** - $999.99
• **Samsung Galaxy S24** - $899.99
• **MacBook Air M3** - $1299.99

🏠 **Smart Home**
• **Smart LED Light Bulb** - $24.99
• **Philips Smart Thermostat** - $199.99

What type of electronics are you most interested in?`
  } else if (lowerMessage.includes("fashion") || lowerMessage.includes("clothing") || lowerMessage.includes("clothes")) {
    response = `Here's our fashion collection:

👟 **Footwear**
• **Blue Running Sneakers** - $89.99
• **Nike Air Max 270** - $129.99
• **Adidas Ultraboost 22** - $179.99

👕 **Clothing**
• **H&M Cotton T-Shirt** - $12.99
• **Classic Denim Jacket** - $79.99
• **Zara Wool Coat** - $199.99

👜 **Accessories**
• **Zara Leather Handbag** - $149.99

What style are you looking for?`
  } else if (lowerMessage.includes("sports") || lowerMessage.includes("fitness") || lowerMessage.includes("gym")) {
    response = `Great choice! Here's our sports & fitness collection:

🏀 **Sports Equipment**
• **Professional Basketball** - $34.99
• **Adidas Soccer Ball** - $29.99
• **Adidas Tennis Racket** - $149.99

💪 **Fitness Gear**
• **Yoga Mat Premium** - $39.99
• **Reebok Dumbbells Set** - $199.99
• **Under Armour Gym Bag** - $49.99

👟 **Athletic Wear**
• **Nike Training Shoes** - $119.99
• **Under Armour Compression Shirt** - $39.99

What sport or activity are you training for?`
  } else if (lowerMessage.includes("home") || lowerMessage.includes("furniture") || lowerMessage.includes("garden")) {
    response = `Here are some great home & garden items:

💡 **Smart Home**
• **Smart LED Light Bulb** - $24.99
• **Philips Air Purifier** - $299.99
• **Philips Smart Thermostat** - $199.99

🪑 **Furniture**
• **IKEA Bookshelf** - $79.99
• **Wayfair Dining Table** - $599.99
• **West Elm Coffee Table** - $399.99

🌱 **Garden**
• **Wayfair Garden Hose** - $39.99
• **IKEA Plant Pot Set** - $29.99

What room are you looking to improve?`
  } else if (lowerMessage.includes("help") || lowerMessage.includes("support")) {
    response = `I'm here to help! I can assist you with:

🛍️ **Product Search**: Find specific products and get recommendations
📱 **Categories**: Browse Electronics, Fashion, Sports, or Home & Garden
🚚 **Shipping**: Free shipping on orders over $50
💳 **Payment**: We accept all major payment methods
📞 **Support**: Call us at +****************

What would you like help with today?`
  } else if (lowerMessage.includes("blue")) {
    response = `Looking for blue items? Here are some great options:

• **Blue Running Sneakers** - $89.99 ⭐
  Comfortable blue running sneakers with advanced cushioning

Perfect for running or casual wear! Would you like to see more blue products?`
  } else if (lowerMessage.includes("cheap") || lowerMessage.includes("affordable") || lowerMessage.includes("budget")) {
    response = `Here are some great budget-friendly options:

💰 **Under $30**
• **H&M Cotton T-Shirt** - $12.99
• **Nike Swim Goggles** - $19.99
• **Smart LED Light Bulb** - $24.99
• **Adidas Soccer Ball** - $29.99

💰 **Under $50**
• **Professional Basketball** - $34.99
• **Yoga Mat Premium** - $39.99
• **Under Armour Gym Bag** - $49.99

What's your budget range?`
  } else {
    response = `Hello! I'm your AI shopping assistant 🤖

I can help you find products from our collection:
📱 **Electronics** - Phones, headphones, smart home
👕 **Fashion** - Clothing, shoes, accessories
🏃 **Sports** - Fitness gear, athletic wear
🏠 **Home** - Furniture, decor, garden items

Try asking me:
• "Show me wireless headphones under $200"
• "I need blue sneakers"
• "What electronics do you have?"

What are you looking for today?`
  }

  return NextResponse.json({
    response,
    sessionId,
  })
}
