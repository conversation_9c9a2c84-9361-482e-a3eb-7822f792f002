import { NextRequest, NextResponse } from "next/server"
import { GoogleGenerative<PERSON>I } from "@google/generative-ai"

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "")

// Product database for context
const PRODUCT_CONTEXT = `
You are an AI shopping assistant for an ecommerce platform. Here are our current products:

ELECTRONICS (12 products):
- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation
- Smart Fitness Watch ($199.99) - Advanced fitness tracking
- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera
- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features
- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip
- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling
- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip
- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen
- LG 55" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV
- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency
- Canon EOS R5 ($3899.99) - Professional mirrorless camera
- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot

FASHION (12 products):
- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning
- Classic Denim Jacket ($79.99) - Timeless denim jacket
- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers
- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost
- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans
- Zara Wool Coat ($199.99) - Elegant wool coat for winter
- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors
- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking
- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket
- Zara Leather Handbag ($149.99) - Premium leather with gold hardware
- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket
- H&M Summer Dress ($39.99) - Flowy summer dress in floral print

SPORTS & FITNESS (12 products):
- Professional Basketball ($34.99) - Official size basketball
- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat
- Nike Training Shoes ($119.99) - Cross-training shoes for gym
- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball
- Under Armour Gym Bag ($49.99) - Durable with multiple compartments
- Puma Running Shorts ($24.99) - Lightweight with built-in brief
- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts
- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles
- Adidas Tennis Racket ($149.99) - Professional with carbon frame
- Under Armour Compression Shirt ($39.99) - Moisture-wicking
- Puma Football Cleats ($89.99) - High-performance football cleats
- Reebok Resistance Bands ($29.99) - Set for strength training

HOME & GARDEN (12 products):
- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb
- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves
- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms
- Wayfair Dining Table ($599.99) - Solid wood dining table for 6
- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows
- West Elm Coffee Table ($399.99) - Mid-century modern coffee table
- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors
- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable
- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle
- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors
- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade
- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage

STORE POLICIES:
- Free shipping on orders over $50
- 30-day return policy
- Standard shipping: 3-5 business days ($5.99)
- Express shipping: 1-2 business days ($12.99)
- International shipping available
- We accept all major credit cards, PayPal, Apple Pay, Google Pay
- Customer service: +****************
- Email: <EMAIL>
`

export async function POST(req: NextRequest) {
  try {
    const { message, sessionId } = await req.json()

    if (!message || !sessionId) {
      return NextResponse.json(
        { error: "Message and sessionId are required" },
        { status: 400 }
      )
    }

    // Check if Gemini API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.warn("GEMINI_API_KEY not found, using intelligent fallback responses")
      return getIntelligentFallbackResponse(message, sessionId)
    }

    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" })

      const prompt = `${PRODUCT_CONTEXT}

You are a friendly, enthusiastic AI shopping assistant named Alex. You LOVE helping people find products and you're genuinely excited about shopping. The customer says: "${message}"

PERSONALITY GUIDELINES:
- Be conversational, warm, and enthusiastic (like talking to a friend who loves shopping)
- Use natural language, contractions, and casual expressions
- Show genuine interest in helping them find the perfect product
- Use emojis naturally but not excessively (1-2 per response)
- Ask follow-up questions to understand their needs better
- Share personal opinions about products ("I love this one!" "This is super popular!")
- Be encouraging and positive

RESPONSE GUIDELINES:
1. If they ask for a product we don't have, acknowledge it honestly and suggest great alternatives
2. When recommending products, explain WHY they're good choices
3. Ask about their specific needs (budget, use case, preferences)
4. Keep responses conversational but informative (3-5 sentences)
5. Always include specific product names and prices from our inventory
6. Guide them naturally toward making a decision

AVOID:
- Robotic or formal language
- Long lists without context
- Generic responses
- Being pushy or sales-y

Respond as Alex, the enthusiastic shopping assistant who genuinely wants to help them find something perfect:`

      const result = await model.generateContent(prompt)
      const response = result.response.text()

      return NextResponse.json({
        response,
        sessionId,
      })
    } catch (aiError) {
      console.error("Gemini AI error:", aiError)
      // Fallback to predefined responses if AI fails
      return getIntelligentFallbackResponse(message, sessionId)
    }

  } catch (error) {
    console.error("Chatbot API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Intelligent fallback function for when AI is not available
function getIntelligentFallbackResponse(message: string, sessionId: string) {
  const lowerMessage = message.toLowerCase()
  let response = ""

  // Greeting responses
  if (lowerMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {
    const greetings = [
      "Hi there! 😊 I'm here to help you find exactly what you're looking for. What's on your shopping list today?",
      "Hello! Great to see you! 👋 I'd love to help you discover some amazing products. What can I help you find?",
      "Hey! Welcome! I'm excited to help you shop. Are you looking for something specific today?",
      "Hi! I'm your personal shopping assistant. What brings you here today - anything particular you need?"
    ]
    response = greetings[Math.floor(Math.random() * greetings.length)]
  }
  // Specific product searches
  else if (lowerMessage.includes("hoodie") || lowerMessage.includes("sweatshirt")) {
    response = `I'd love to help you find a hoodie! 😊

Unfortunately, I don't see any hoodies in our current collection, but I do have some great alternatives that might work:

• **Adidas Track Jacket** - $79.99
  Classic three-stripe design, perfect for casual wear

• **Zara Wool Coat** - $199.99
  Cozy and warm for cooler weather

• **Classic Denim Jacket** - $79.99
  Timeless style that goes with everything

Would any of these work for you, or are you specifically looking for a pullover hoodie? I can also check if we're getting any hoodies in stock soon! 🤔`
  }
  else if (lowerMessage.includes("jacket") && !lowerMessage.includes("denim")) {
    response = `Great choice! I have some fantastic jackets for you:

• **Adidas Track Jacket** - $79.99 ⭐
  Classic athletic style with the iconic three stripes

• **Classic Denim Jacket** - $79.99
  Timeless denim that never goes out of style

• **Zara Wool Coat** - $199.99
  Perfect for colder weather, very elegant

What style are you going for - sporty, casual, or more formal? That'll help me point you to the perfect one! 😊`
  }
  else if (lowerMessage.includes("t-shirt") || lowerMessage.includes("tshirt") || lowerMessage.includes("shirt")) {
    response = `Perfect! I have just what you need:

• **H&M Cotton T-Shirt** - $12.99 ✨
  Super comfortable basic cotton tee, available in multiple colors

• **Under Armour Compression Shirt** - $39.99
  Great for workouts with moisture-wicking technology

The H&M cotton tee is really popular - it's soft, affordable, and comes in lots of colors. What color were you thinking? 🎨`
  }
  // Price-based queries
  else if (lowerMessage.includes("under") || lowerMessage.includes("below") || lowerMessage.includes("less than")) {
    const priceMatch = lowerMessage.match(/\$?(\d+)/)
    const budget = priceMatch ? parseInt(priceMatch[1]) : 100

    if (lowerMessage.includes("headphones") || lowerMessage.includes("earbuds")) {
      if (budget >= 200) {
        response = `Awesome! You've got a good budget for some really nice headphones! 🎧

Here's what I'd recommend under $${budget}:

• **Wireless Bluetooth Headphones** - $99.99 ⭐
  These are fantastic! Premium sound with noise cancellation, and they're our bestseller

• **AirPods Pro 2** - $249.99
  If you're an Apple user, these are incredible with adaptive transparency

• **Sony WH-1000XM5** - $349.99
  A bit over budget but worth mentioning - industry-leading noise canceling

Honestly, the Wireless Bluetooth ones at $99.99 are amazing value. Great sound quality and the noise cancellation really works! What do you think? 😊`
      } else {
        response = `Perfect! I have exactly what you need under $${budget}:

• **Wireless Bluetooth Headphones** - $99.99 🎧
  These are seriously good! Premium wireless with noise cancellation

They're our most popular headphones and for good reason - great sound, comfortable for hours, and the noise cancellation is surprisingly effective for the price. Plus they're well within your budget!

Are you planning to use them mainly for music, calls, or maybe working out? 🤔`
      }
    } else if (lowerMessage.includes("electronics") || lowerMessage.includes("tech")) {
      response = `Here are some great electronics under $${budget}:

• **Smart LED Light Bulb** - $24.99
  WiFi-enabled smart LED bulb with color changing

• **Nike Swim Goggles** - $19.99
  Anti-fog swimming goggles

${budget >= 100 ? '• **Wireless Bluetooth Headphones** - $99.99\n  Premium wireless headphones with noise cancellation' : ''}

What type of electronics are you most interested in?`
    } else {
      response = `I can help you find great products under $${budget}! Here are some popular options:

• **H&M Cotton T-Shirt** - $12.99 (Fashion)
• **Smart LED Light Bulb** - $24.99 (Home)
• **Adidas Soccer Ball** - $29.99 (Sports)
${budget >= 50 ? '• **Under Armour Gym Bag** - $49.99 (Sports)' : ''}
${budget >= 90 ? '• **Blue Running Sneakers** - $89.99 (Fashion)' : ''}

What category interests you most?`
    }
  } else if (lowerMessage.includes("sneakers") || lowerMessage.includes("shoes")) {
    response = `Oh nice! I love helping people find the perfect sneakers! 👟

Here are some awesome options I think you'll love:

• **Blue Running Sneakers** - $89.99 ⭐
  These are super comfortable with amazing cushioning - great for running or just everyday wear

• **Nike Air Max 270** - $129.99
  Classic Nike style! The Air Max cushioning feels like walking on clouds

• **Adidas Ultraboost 22** - $179.99
  Premium choice with Boost technology - runners absolutely love these

Are you looking for something more for running, casual wear, or maybe gym workouts? That'll help me narrow down the perfect pair for you! 😊`
  } else if (lowerMessage.includes("electronics") || lowerMessage.includes("tech") || lowerMessage.includes("gadgets")) {
    response = `Ooh, a tech lover! 🤓 I've got some really cool electronics that might catch your eye:

🎧 **Audio stuff** (my personal favorites!)
• **Wireless Bluetooth Headphones** - $99.99 - Amazing sound quality
• **Sony WH-1000XM5** - $349.99 - The noise canceling is incredible
• **AirPods Pro 2** - $249.99 - Perfect if you're in the Apple ecosystem

📱 **Phones & Computers**
• **iPhone 15 Pro** - $999.99 - Latest and greatest from Apple
• **Samsung Galaxy S24** - $899.99 - Fantastic Android with AI features
• **MacBook Air M3** - $1299.99 - Super thin and crazy fast

🏠 **Smart Home** (these are fun!)
• **Smart LED Light Bulb** - $24.99 - Changes colors with your phone
• **Philips Smart Thermostat** - $199.99 - Saves money on energy bills

What kind of tech are you in the mood for? Looking to upgrade something specific? 😊`
  } else if (lowerMessage.includes("fashion") || lowerMessage.includes("clothing") || lowerMessage.includes("clothes")) {
    response = `Fashion time! 👗 I love helping people put together great looks!

Here's what's popular right now:

👟 **Shoes** (always a good place to start!)
• **Blue Running Sneakers** - $89.99 - Super versatile, goes with everything
• **Nike Air Max 270** - $129.99 - Classic style that never gets old
• **Adidas Ultraboost 22** - $179.99 - Sporty but stylish

👕 **Clothing**
• **H&M Cotton T-Shirt** - $12.99 - Perfect basic, comes in tons of colors
• **Classic Denim Jacket** - $79.99 - Timeless piece, works with any outfit
• **Zara Wool Coat** - $199.99 - So elegant and cozy for cooler weather

👜 **Accessories**
• **Zara Leather Handbag** - $149.99 - Beautiful quality, very chic

What's your style vibe? Casual and comfy, or are you looking to dress up a bit? 😊`
  } else if (lowerMessage.includes("sports") || lowerMessage.includes("fitness") || lowerMessage.includes("gym")) {
    response = `Great choice! Here's our sports & fitness collection:

🏀 **Sports Equipment**
• **Professional Basketball** - $34.99
• **Adidas Soccer Ball** - $29.99
• **Adidas Tennis Racket** - $149.99

💪 **Fitness Gear**
• **Yoga Mat Premium** - $39.99
• **Reebok Dumbbells Set** - $199.99
• **Under Armour Gym Bag** - $49.99

👟 **Athletic Wear**
• **Nike Training Shoes** - $119.99
• **Under Armour Compression Shirt** - $39.99

What sport or activity are you training for?`
  } else if (lowerMessage.includes("home") || lowerMessage.includes("furniture") || lowerMessage.includes("garden")) {
    response = `Here are some great home & garden items:

💡 **Smart Home**
• **Smart LED Light Bulb** - $24.99
• **Philips Air Purifier** - $299.99
• **Philips Smart Thermostat** - $199.99

🪑 **Furniture**
• **IKEA Bookshelf** - $79.99
• **Wayfair Dining Table** - $599.99
• **West Elm Coffee Table** - $399.99

🌱 **Garden**
• **Wayfair Garden Hose** - $39.99
• **IKEA Plant Pot Set** - $29.99

What room are you looking to improve?`
  } else if (lowerMessage.includes("help") || lowerMessage.includes("support")) {
    response = `Of course! I'm here to help with whatever you need! 😊

I can help you with:
🛍️ **Finding products** - Just tell me what you're looking for
💰 **Budget questions** - I'll find great options in your price range
🚚 **Shipping info** - We have free shipping on orders over $50!
💳 **Payment stuff** - We accept pretty much everything
📞 **Other questions** - Call our team at +****************

What's on your mind? I'm here to make your shopping super easy! 🛒`
  } else if (lowerMessage.includes("blue")) {
    response = `Looking for blue items? Here are some great options:

• **Blue Running Sneakers** - $89.99 ⭐
  Comfortable blue running sneakers with advanced cushioning

Perfect for running or casual wear! Would you like to see more blue products?`
  } else if (lowerMessage.includes("cheap") || lowerMessage.includes("affordable") || lowerMessage.includes("budget")) {
    response = `Here are some great budget-friendly options:

💰 **Under $30**
• **H&M Cotton T-Shirt** - $12.99
• **Nike Swim Goggles** - $19.99
• **Smart LED Light Bulb** - $24.99
• **Adidas Soccer Ball** - $29.99

💰 **Under $50**
• **Professional Basketball** - $34.99
• **Yoga Mat Premium** - $39.99
• **Under Armour Gym Bag** - $49.99

What's your budget range?`
  } else {
    // Handle unknown queries more naturally
    const unknownResponses = [
      `Hmm, I'm not sure I caught that exactly! 🤔 Could you tell me a bit more about what you're looking for? I've got electronics, fashion, sports gear, and home stuff!`,
      `I want to help you find something great! Could you be a bit more specific? Are you shopping for yourself or maybe looking for a gift? 😊`,
      `I'm here to help! What kind of product are you in the mood for today? Electronics, clothes, sports stuff, or maybe something for your home? 🛍️`,
      `Let me help you find exactly what you need! What's on your shopping list today? I love helping people discover cool products! ✨`
    ]
    response = unknownResponses[Math.floor(Math.random() * unknownResponses.length)]
  }

  return NextResponse.json({
    response,
    sessionId,
  })
}
