import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function POST(req: NextRequest) {
  try {
    const { message, sessionId } = await req.json()

    if (!message || !sessionId) {
      return NextResponse.json(
        { error: "Message and sessionId are required" },
        { status: 400 }
      )
    }

    // Simple AI logic for product recommendations
    const lowerMessage = message.toLowerCase()
    let response = ""

    // Check for product searches
    if (lowerMessage.includes("sneakers") || lowerMessage.includes("shoes")) {
      // Mock sneakers data
      const sneakers = [
        { name: "Blue Running Sneakers", price: 89.99, description: "Comfortable blue running sneakers with advanced cushioning" },
        { name: "Classic White Sneakers", price: 79.99, description: "Timeless white sneakers perfect for everyday wear" },
      ]

      response = `I found some great sneakers for you! Here are my top recommendations:\n\n`
      sneakers.forEach((sneaker, index) => {
        response += `${index + 1}. **${sneaker.name}** - $${sneaker.price}\n`
        response += `   ${sneaker.description}\n\n`
      })
      response += `Would you like to see more details about any of these products? You can also visit our products page to see the full collection!`
    } else if (lowerMessage.includes("blue") && (lowerMessage.includes("sneakers") || lowerMessage.includes("shoes"))) {
      // Mock blue sneakers data
      const blueShoes = [
        { name: "Blue Running Sneakers", price: 89.99, description: "Comfortable blue running sneakers with advanced cushioning and breathable mesh" },
      ]

      response = `Perfect! I found some blue sneakers that might interest you:\n\n`
      blueShoes.forEach((shoe, index) => {
        response += `${index + 1}. **${shoe.name}** - $${shoe.price}\n`
        response += `   ${shoe.description}\n\n`
      })
      response += `These blue sneakers are popular choices! Would you like to know more about any specific pair?`
    } else if (lowerMessage.includes("electronics") || lowerMessage.includes("gadgets") || lowerMessage.includes("tech")) {
      // Mock electronics data
      const electronics = [
        { name: "Wireless Bluetooth Headphones", price: 99.99, description: "Premium wireless headphones with noise cancellation" },
        { name: "Smart Fitness Watch", price: 199.99, description: "Advanced fitness tracking smartwatch" },
        { name: "Smart LED Light Bulb", price: 24.99, description: "WiFi-enabled smart LED bulb" },
      ]

      response = `Here are some popular electronics from our collection:\n\n`
      electronics.forEach((item, index) => {
        response += `${index + 1}. **${item.name}** - $${item.price}\n`
        response += `   ${item.description}\n\n`
      })
      response += `These are some of our best-selling tech products! Interested in learning more about any of them?`
    } else if (lowerMessage.includes("help") || lowerMessage.includes("support")) {
      response = `I'm here to help! Here's what I can assist you with:

🛍️ **Product Search** - Tell me what you're looking for and I'll find the best options
📱 **Product Information** - Ask about specific products, prices, or features  
🚚 **Shipping & Returns** - Get info about delivery and return policies
💳 **Payment** - Learn about payment options and security
🎯 **Recommendations** - Get personalized product suggestions

What would you like help with today?`
    } else if (lowerMessage.includes("price") || lowerMessage.includes("cost") || lowerMessage.includes("cheap") || lowerMessage.includes("affordable")) {
      response = `I can help you find products within your budget! Here are some ways to find great deals:

💰 **Budget-Friendly Options** - Check our Deals page for discounts
🏷️ **Sort by Price** - Use the price filter on our products page
🔥 **Featured Deals** - Look for products marked as "Featured" for special offers
📦 **Bundle Deals** - Some products offer better value when bought together

What's your budget range? I can help you find the best options!`
    } else if (lowerMessage.includes("shipping") || lowerMessage.includes("delivery")) {
      response = `Here's everything you need to know about shipping:

🚚 **Free Shipping** - On orders over $50
⚡ **Fast Delivery** - 2-3 business days for most items
📦 **Tracking** - You'll get a tracking number once your order ships
🌍 **Worldwide** - We ship to most countries

Need help with a specific order or have questions about delivery times?`
    } else {
      // General response
      response = `Hi there! I'm your AI shopping assistant. I can help you:

🔍 Find specific products (just tell me what you're looking for!)
💡 Get product recommendations based on your needs
💰 Find the best deals and offers
📋 Answer questions about products, shipping, and more

What are you shopping for today? For example, you could say "I'm looking for blue sneakers" or "Show me electronics under $100"`
    }

    // Note: In production, you would save the conversation to database

    return NextResponse.json({ response })
  } catch (error) {
    console.error("Chatbot API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
