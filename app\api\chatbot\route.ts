import { NextRequest, NextResponse } from "next/server"
import { GoogleGenerative<PERSON>I } from "@google/generative-ai"

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "")

// Product database for context
const PRODUCT_CONTEXT = `
You are an AI shopping assistant for an ecommerce platform. Here are our current products:

ELECTRONICS (12 products):
- Wireless Bluetooth Headphones ($99.99) - Premium noise cancellation
- Smart Fitness Watch ($199.99) - Advanced fitness tracking
- iPhone 15 Pro ($999.99) - Latest iPhone with advanced camera
- Samsung Galaxy S24 ($899.99) - Flagship Android with AI features
- MacBook Air M3 ($1299.99) - Ultra-thin laptop with M3 chip
- Sony WH-1000XM5 ($349.99) - Industry-leading noise canceling
- iPad Pro 12.9 ($1099.99) - Professional tablet with M2 chip
- Nintendo Switch OLED ($349.99) - Gaming console with OLED screen
- LG 55" 4K OLED TV ($1499.99) - Premium 4K OLED smart TV
- AirPods Pro 2 ($249.99) - Wireless earbuds with transparency
- Canon EOS R5 ($3899.99) - Professional mirrorless camera
- Tesla Model S Plaid ($89999.99) - Electric vehicle with autopilot

FASHION (12 products):
- Blue Running Sneakers ($89.99) - Comfortable with advanced cushioning
- Classic Denim Jacket ($79.99) - Timeless denim jacket
- Nike Air Max 270 ($129.99) - Iconic lifestyle sneakers
- Adidas Ultraboost 22 ($179.99) - Premium running shoes with Boost
- Levi's 501 Original Jeans ($69.99) - Classic straight-leg jeans
- Zara Wool Coat ($199.99) - Elegant wool coat for winter
- H&M Cotton T-Shirt ($12.99) - Basic cotton t-shirt, multiple colors
- Nike Dri-FIT Shorts ($34.99) - Athletic shorts with moisture-wicking
- Adidas Track Jacket ($79.99) - Classic three-stripe track jacket
- Zara Leather Handbag ($149.99) - Premium leather with gold hardware
- Levi's Trucker Jacket ($89.99) - Iconic denim trucker jacket
- H&M Summer Dress ($39.99) - Flowy summer dress in floral print

SPORTS & FITNESS (12 products):
- Professional Basketball ($34.99) - Official size basketball
- Yoga Mat Premium ($39.99) - Non-slip premium yoga mat
- Nike Training Shoes ($119.99) - Cross-training shoes for gym
- Adidas Soccer Ball ($29.99) - FIFA-approved soccer ball
- Under Armour Gym Bag ($49.99) - Durable with multiple compartments
- Puma Running Shorts ($24.99) - Lightweight with built-in brief
- Reebok Dumbbells Set ($199.99) - Adjustable for home workouts
- Nike Swim Goggles ($19.99) - Anti-fog swimming goggles
- Adidas Tennis Racket ($149.99) - Professional with carbon frame
- Under Armour Compression Shirt ($39.99) - Moisture-wicking
- Puma Football Cleats ($89.99) - High-performance football cleats
- Reebok Resistance Bands ($29.99) - Set for strength training

HOME & GARDEN (12 products):
- Smart LED Light Bulb ($24.99) - WiFi-enabled smart LED bulb
- IKEA Bookshelf ($79.99) - Modern wooden bookshelf, 5 shelves
- Philips Air Purifier ($299.99) - HEPA air purifier for large rooms
- Wayfair Dining Table ($599.99) - Solid wood dining table for 6
- Pottery Barn Throw Pillows ($89.99) - Set of 4 decorative pillows
- West Elm Coffee Table ($399.99) - Mid-century modern coffee table
- IKEA Kitchen Cabinet ($149.99) - Modular with soft-close doors
- Philips Smart Thermostat ($199.99) - WiFi-enabled programmable
- Wayfair Garden Hose ($39.99) - 50ft expandable with spray nozzle
- Pottery Barn Area Rug ($299.99) - Hand-woven in neutral colors
- West Elm Floor Lamp ($199.99) - Modern brass with fabric shade
- IKEA Plant Pot Set ($29.99) - Set of 3 ceramic pots with drainage

STORE POLICIES:
- Free shipping on orders over $50
- 30-day return policy
- Standard shipping: 3-5 business days ($5.99)
- Express shipping: 1-2 business days ($12.99)
- International shipping available
- We accept all major credit cards, PayPal, Apple Pay, Google Pay
- Customer service: +****************
- Email: <EMAIL>
`

export async function POST(req: NextRequest) {
  try {
    const { message, sessionId } = await req.json()

    if (!message || !sessionId) {
      return NextResponse.json(
        { error: "Message and sessionId are required" },
        { status: 400 }
      )
    }

    // Check if Gemini API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.warn("GEMINI_API_KEY not found, using intelligent fallback responses")
      return await getIntelligentFallbackResponse(message, sessionId)
    }

    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" })

      const prompt = `${PRODUCT_CONTEXT}

You are a friendly, enthusiastic AI shopping assistant named Alex. You LOVE helping people find products and you're genuinely excited about shopping. The customer says: "${message}"

PERSONALITY GUIDELINES:
- Be conversational, warm, and enthusiastic (like talking to a friend who loves shopping)
- Use natural language, contractions, and casual expressions
- Show genuine interest in helping them find the perfect product
- Use emojis naturally but not excessively (1-2 per response)
- Ask follow-up questions to understand their needs better
- Share personal opinions about products ("I love this one!" "This is super popular!")
- Be encouraging and positive

RESPONSE GUIDELINES:
1. If they ask for a product we don't have, acknowledge it honestly and suggest great alternatives
2. When recommending products, explain WHY they're good choices
3. Ask about their specific needs (budget, use case, preferences)
4. Keep responses conversational but informative (3-5 sentences)
5. Always include specific product names and prices from our inventory
6. Guide them naturally toward making a decision

AVOID:
- Robotic or formal language
- Long lists without context
- Generic responses
- Being pushy or sales-y

Respond as Alex, the enthusiastic shopping assistant who genuinely wants to help them find something perfect:`

      const result = await model.generateContent(prompt)
      const response = result.response.text()

      return NextResponse.json({
        response,
        sessionId,
      })
    } catch (aiError) {
      console.error("Gemini AI error:", aiError)
      // Fallback to predefined responses if AI fails
      return await getIntelligentFallbackResponse(message, sessionId)
    }

  } catch (error) {
    console.error("Chatbot API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// Dynamic product search function
async function searchProducts(query: string, limit: number = 5) {
  try {
    const searchParams = new URLSearchParams({
      search: query,
      limit: limit.toString()
    })

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products?${searchParams}`)
    const data = await response.json()
    return data.products || []
  } catch (error) {
    console.error('Error searching products:', error)
    return []
  }
}

// Dynamic category search function
async function getProductsByCategory(category: string, limit: number = 5) {
  try {
    const searchParams = new URLSearchParams({
      category: category,
      limit: limit.toString()
    })

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products?${searchParams}`)
    const data = await response.json()
    return data.products || []
  } catch (error) {
    console.error('Error fetching category products:', error)
    return []
  }
}

// Dynamic price-based search function
async function getProductsByPriceRange(maxPrice: number, category?: string, limit: number = 5) {
  try {
    const searchParams = new URLSearchParams({
      maxPrice: maxPrice.toString(),
      limit: limit.toString()
    })

    if (category) {
      searchParams.append('category', category)
    }

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products?${searchParams}`)
    const data = await response.json()
    return data.products || []
  } catch (error) {
    console.error('Error fetching products by price:', error)
    return []
  }
}

// Helper function to check if message contains product search intent
async function hasProductSearch(message: string): Promise<boolean> {
  const productKeywords = [
    'looking for', 'need', 'want', 'show me', 'find', 'search', 'buy',
    'hoodie', 'jacket', 'shirt', 'shoes', 'sneakers', 'headphones', 'phone',
    'laptop', 'watch', 'bag', 'dress', 'jeans', 'coat', 'electronics',
    'fashion', 'sports', 'home', 'furniture', 'tech', 'gadgets'
  ]

  return productKeywords.some(keyword => message.toLowerCase().includes(keyword))
}

// Dynamic product search handler
async function handleProductSearch(message: string): Promise<string> {
  const lowerMessage = message.toLowerCase()

  // Extract price if mentioned
  const priceMatch = lowerMessage.match(/under|below|less than.*?\$?(\d+)/)
  const maxPrice = priceMatch ? parseInt(priceMatch[1]) : null

  // Extract category hints
  let category = null
  if (lowerMessage.includes('electronics') || lowerMessage.includes('tech') || lowerMessage.includes('gadgets') ||
      lowerMessage.includes('phone') || lowerMessage.includes('laptop') || lowerMessage.includes('headphones')) {
    category = 'electronics'
  } else if (lowerMessage.includes('fashion') || lowerMessage.includes('clothing') || lowerMessage.includes('clothes') ||
             lowerMessage.includes('shirt') || lowerMessage.includes('jacket') || lowerMessage.includes('shoes') ||
             lowerMessage.includes('sneakers') || lowerMessage.includes('dress') || lowerMessage.includes('jeans')) {
    category = 'fashion'
  } else if (lowerMessage.includes('sports') || lowerMessage.includes('fitness') || lowerMessage.includes('gym') ||
             lowerMessage.includes('basketball') || lowerMessage.includes('soccer') || lowerMessage.includes('yoga')) {
    category = 'sports'
  } else if (lowerMessage.includes('home') || lowerMessage.includes('furniture') || lowerMessage.includes('garden') ||
             lowerMessage.includes('lamp') || lowerMessage.includes('table') || lowerMessage.includes('chair')) {
    category = 'home'
  }

  let products = []

  // Search strategy: try specific search first, then category, then price range
  if (maxPrice && category) {
    products = await getProductsByPriceRange(maxPrice, category, 4)
  } else if (maxPrice) {
    products = await getProductsByPriceRange(maxPrice, undefined, 4)
  } else if (category) {
    products = await getProductsByCategory(category, 4)
  } else {
    // Extract key search terms
    const searchTerms = lowerMessage.replace(/looking for|need|want|show me|find|search|buy/g, '').trim()
    products = await searchProducts(searchTerms, 4)
  }

  // Generate dynamic response based on results
  if (products.length === 0) {
    return generateNoResultsResponse(lowerMessage, category)
  } else {
    return generateProductResponse(products, lowerMessage, maxPrice, category)
  }
}

// Generate response when no products found
function generateNoResultsResponse(query: string, category: string | null): string {
  const responses = [
    `I'd love to help you find what you're looking for! 😊 I don't see exactly that in our current collection, but let me suggest some alternatives that might work for you. What specific features are most important to you?`,
    `Hmm, I don't have exactly that right now, but I might have something similar! 🤔 Could you tell me a bit more about what you need it for? That'll help me find the perfect alternative!`,
    `I want to help you find something great! While I don't have exactly that item, I have some similar options that might be even better. What's your budget range?`
  ]

  return responses[Math.floor(Math.random() * responses.length)]
}

// Generate response with product recommendations
function generateProductResponse(products: any[], query: string, maxPrice: number | null, category: string | null): string {
  const enthusiasm = ['Great choice!', 'Perfect!', 'Awesome!', 'I found some great options!', 'You\'re going to love these!']
  const intro = enthusiasm[Math.floor(Math.random() * enthusiasm.length)]

  let response = `${intro} 😊 Here's what I found for you:\n\n`

  products.forEach((product, index) => {
    const emoji = index === 0 ? ' ⭐' : ''
    response += `• **${product.name}** - $${product.price}${emoji}\n`
    if (product.description) {
      response += `  ${product.description}\n\n`
    }
  })

  // Add contextual follow-up based on search
  if (maxPrice) {
    response += `All of these fit within your $${maxPrice} budget! `
  }

  if (products.length > 1) {
    response += `Which one catches your eye? I can tell you more about any of them! 🤔`
  } else {
    response += `This is really popular! Would you like to know more about the features? 😊`
  }

  return response
}

// Intelligent fallback function for when AI is not available
async function getIntelligentFallbackResponse(message: string, sessionId: string) {
  const lowerMessage = message.toLowerCase()
  let response = ""

  // Greeting responses
  if (lowerMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {
    const greetings = [
      "Hi there! 😊 I'm here to help you find exactly what you're looking for. What's on your shopping list today?",
      "Hello! Great to see you! 👋 I'd love to help you discover some amazing products. What can I help you find?",
      "Hey! Welcome! I'm excited to help you shop. Are you looking for something specific today?",
      "Hi! I'm your personal shopping assistant. What brings you here today - anything particular you need?"
    ]
    response = greetings[Math.floor(Math.random() * greetings.length)]
  }
  // Dynamic product searches
  else if (await hasProductSearch(lowerMessage)) {
    const searchResults = await handleProductSearch(lowerMessage)
    response = searchResults
  }
  // Handle category browsing
  else if (lowerMessage.includes("electronics") || lowerMessage.includes("tech") || lowerMessage.includes("gadgets")) {
    const products = await getProductsByCategory('electronics', 5)
    if (products.length > 0) {
      response = `Ooh, a tech lover! 🤓 Here are some cool electronics I think you'll love:\n\n`
      products.forEach((product, index) => {
        const emoji = index === 0 ? ' ⭐' : ''
        response += `• **${product.name}** - $${product.price}${emoji}\n`
        if (product.description) response += `  ${product.description}\n\n`
      })
      response += `What kind of tech are you in the mood for? Looking to upgrade something specific? 😊`
    } else {
      response = `I'd love to show you our electronics! Let me check what's available right now... 🤔`
    }
  }
  else if (lowerMessage.includes("fashion") || lowerMessage.includes("clothing") || lowerMessage.includes("clothes")) {
    const products = await getProductsByCategory('fashion', 5)
    if (products.length > 0) {
      response = `Fashion time! 👗 Here's what's trending right now:\n\n`
      products.forEach((product, index) => {
        const emoji = index === 0 ? ' ⭐' : ''
        response += `• **${product.name}** - $${product.price}${emoji}\n`
        if (product.description) response += `  ${product.description}\n\n`
      })
      response += `What's your style vibe? Casual and comfy, or looking to dress up a bit? 😊`
    } else {
      response = `I'd love to help you with fashion! Let me see what styles we have available... 👗`
    }
  }
  else if (lowerMessage.includes("sports") || lowerMessage.includes("fitness") || lowerMessage.includes("gym")) {
    const products = await getProductsByCategory('sports', 5)
    if (products.length > 0) {
      response = `Great choice! Here's our sports & fitness collection:\n\n`
      products.forEach((product, index) => {
        const emoji = index === 0 ? ' ⭐' : ''
        response += `• **${product.name}** - $${product.price}${emoji}\n`
        if (product.description) response += `  ${product.description}\n\n`
      })
      response += `What sport or activity are you training for? 💪`
    } else {
      response = `I'd love to help you with sports gear! Let me check our current selection... 🏃‍♂️`
    }
  }
  else if (lowerMessage.includes("home") || lowerMessage.includes("furniture") || lowerMessage.includes("garden")) {
    const products = await getProductsByCategory('home', 5)
    if (products.length > 0) {
      response = `Perfect! Here are some great home & garden items:\n\n`
      products.forEach((product, index) => {
        const emoji = index === 0 ? ' ⭐' : ''
        response += `• **${product.name}** - $${product.price}${emoji}\n`
        if (product.description) response += `  ${product.description}\n\n`
      })
      response += `What room are you looking to improve? 🏠`
    } else {
      response = `I'd love to help you with home items! Let me see what we have... 🏠`
    }
  }
  else if (lowerMessage.includes("help") || lowerMessage.includes("support")) {
    response = `Of course! I'm here to help with whatever you need! 😊

I can help you with:
🛍️ **Finding products** - Just tell me what you're looking for
💰 **Budget questions** - I'll find great options in your price range
🚚 **Shipping info** - We have free shipping on orders over $50!
💳 **Payment stuff** - We accept pretty much everything
📞 **Other questions** - Call our team at +****************

What's on your mind? I'm here to make your shopping super easy! 🛒`
  }
  else {
    // Handle unknown queries more naturally
    const unknownResponses = [
      `Hmm, I'm not sure I caught that exactly! 🤔 Could you tell me a bit more about what you're looking for? I've got electronics, fashion, sports gear, and home stuff!`,
      `I want to help you find something great! Could you be a bit more specific? Are you shopping for yourself or maybe looking for a gift? 😊`,
      `I'm here to help! What kind of product are you in the mood for today? Electronics, clothes, sports stuff, or maybe something for your home? 🛍️`,
      `Let me help you find exactly what you need! What's on your shopping list today? I love helping people discover cool products! ✨`
    ]
    response = unknownResponses[Math.floor(Math.random() * unknownResponses.length)]
  }

  return NextResponse.json({
    response,
    sessionId,
  })
}
